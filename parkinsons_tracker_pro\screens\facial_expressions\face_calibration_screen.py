from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image
from utils import read_asset_image
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)) + "/../..")
from screens.task_background_screen import TaskBackgroundContainer
from screens.common_screen_element import TaskTitle
from task_data import messages


class FaceVisibilityContainer(Container):
    def __init__(self, rect=[0, 0, 495, 104], radius=0, border_color=None):
        super().__init__(rect)
        self.border_color = border_color
        self.corner_radius = radius
        self.alpha = 0

        self.not_visible_icon = Image(
            [20, 0, 56, 56], data=read_asset_image("warning.png")
        )

        self.visible_icon = Image(
            [20, 0, 56, 56], data=read_asset_image("ok.png"))
        self.visible_icon.visible = False

        self.title_label = Label(
            [0, 4, 495, 54],
            data=messages["face_visible"],
        )
        self.title_label.font_size = 40
        self.title_label.alignment = "center"
        self.title_label.font_weight = "bold"

        self.hint_label = Label(
            [0, 56, 495, 48],
            data=messages["move_left"],
        )
        self.hint_label.font_size = 36
        self.hint_label.alignment = "center"

        self.add_children(
            [self.not_visible_icon, self.visible_icon, self.title_label, self.hint_label]
        )

    def is_visible(self):
        self.not_visible_icon.visible = False
        for i in range(len(self.children)):
            if self.children[i]._id == self.not_visible_icon._id:
                self.children[i].visible = False
                break
        self.root_widget_tree.update(self.not_visible_icon)
        self.visible_icon.visible = True
        for i in range(len(self.children)):
            if self.children[i]._id == self.visible_icon._id:
                self.children[i].visible = True
                break
        self.root_widget_tree.update(self.visible_icon)
        self.root_widget_tree.update(self)

    def not_visible(self):
        self.not_visible_icon.visible = True
        for i in range(len(self.children)):
            if self.children[i]._id == self.not_visible_icon._id:
                self.children[i].visible = True
                break
        self.root_widget_tree.update(self.not_visible_icon)
        self.visible_icon.visible = False
        for i in range(len(self.children)):
            if self.children[i]._id == self.visible_icon._id:
                self.children[i].visible = False
                break
        self.root_widget_tree.update(self.visible_icon)
        self.root_widget_tree.update(self)


class FaceCalibrationScreen(Container):
    def __init__(self, rect=[0, 0, 1920, 918], on_complete=None, radius=0, border_color=None):
        super().__init__([0, 0, 1920, 1080])
        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name="Facial Expression",
                                                           task_icon=read_asset_image("facial.png")))
        
        self.content = Container([0, 100, 1920, 918])
        
        self.content.border_color = border_color
        self.content.corner_radius = radius
        self.content.alpha = 0

        self.face_title_label = Label(
            [472.275, 15, 975.45, 60],
            data=messages["face_calib_title"],
        )
        self.face_title_label.font_size = 46.67
        self.face_title_label.alignment = "center"
        self.face_title_label.font_weight = "bold"

        self.description_label = Label(
            [472.275, 100, 975.45, 44],
            data=messages["face_hint"],
        )
        self.description_label.font_size = 36
        self.description_label.alignment = "center"

        self.face_visibility_container = FaceVisibilityContainer(
            [712.5, 170, 495, 104])
        
        self.main_camera_image = Image(
            [472.275, 328.85, 975.45, 549.15]
        )

        self.on_complete = on_complete

        self.content.add_children(
            [self.face_title_label, self.description_label, self.face_visibility_container, self.main_camera_image]
        )

        self.task_background.update_content(self.content)
        self.add_child(self.task_background)
