from cortic_platform.sdk.ui.basic_widgets import Container, Image, Label
from cortic_platform.sdk.ui.input_widgets import <PERSON><PERSON>, PinPut
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from main_background_screen import MainBackgroundContainer
from common_utils import *


class LoginScreen(Container):
    def __init__(self):
        super().__init__([0, 0, 1920, 1080])
        
        self.background = MainBackgroundContainer([0, 0, 1920, 1080])

        self.login_dialog = LoginDialog(
            [694, 340.65, 532, 398.7], radius=30
        )

        self.patient_verify_screen = PatientVerifyDialog(
            [720, 390.65, 480, 298.7],
            radius=30
        )
        self.patient_verify_screen.visible = False

        self.popup_screen = MessagePopup(
            [810, 440, 300, 200], radius=30
        )
        self.popup_screen.visible = False

        self.background.add_children([self.login_dialog, self.patient_verify_screen, self.popup_screen])

        self.add_child(self.background)

class LoginDialog(Container):
    def __init__(self, rect=[0, 0, 532, 398.7], radius=0, border_color=None, on_login_btn_pressed=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.on_login_btn_pressed = on_login_btn_pressed

        self.logo_image = Image(
            [198.5, 16, 135, 154.7], data=read_asset_image("McKeown_logo.png")
        )

        self.id_label = Label(
            [70, 226.7, 110, 48],
            data="Patient PIN:"
        )
        self.id_label.font_color = "#9E9E9E"
        self.id_label.font_size = 18

        self.id_field = PinPut(
            [185, 226.7, 230, 48]
        )
        self.id_field.num_fields=5
        self.id_field.field_width = 38
        self.id_field.field_height = 45
        self.id_field.field_separation = 7
        self.id_field.font_size = 18
        self.id_field.font_color = "#9E9E9E"
        self.id_field.following_field_border_color = "#9E9E9E"

        self.login_btn = Button([223.5, 334.7, 85, 48])
        self.login_btn.label = "Log In"
        self.login_btn.on_widget_event = self.on_login_btn_pressed

        self.add_children([self.logo_image, self.id_label, self.id_field, self.login_btn])

    def set_login_btn_callback(self, callback):
        self.on_login_btn_pressed = callback
        self.login_btn.on_widget_event = self.on_login_btn_pressed

class PatientVerifyDialog(Container):
    def __init__(
        self,
        rect=[0, 0, 480, 298.7],
        radius=0,
        border_color=None,
        on_yes_btn_pressed=None,
        on_no_btn_pressed=None,
    ):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.on_yes_btn_pressed = on_yes_btn_pressed
        self.on_no_btn_pressed = on_no_btn_pressed
        self.logo_image = Image(
            [20, 20, 224.24, 58.13],
            data=read_asset_image("McKeown_logo_horizontal.png"),
        )

        self.confirm_text = Label(
            [0, 120, 532, 50], data=""
        )
        self.confirm_text.alignment = "center"
        self.confirm_text.font_size = 26

        self.no_btn = Button(
            [100, 220, 120, 48]
        )
        self.no_btn.label = "No"
        self.no_btn.button_color = "#E21921"
        self.no_btn.font_size = 18
        self.no_btn.on_widget_event = self.on_no_btn_pressed

        self.yes_btn = Button(
            [260, 220, 120, 48]
        )
        self.yes_btn.label = "Yes"
        self.yes_btn.font_size = 18
        self.yes_btn.on_widget_event = self.on_yes_btn_pressed

        self.add_children([self.logo_image, self.confirm_text, self.no_btn, self.yes_btn])

    def set_btn_callback(self, on_yes_btn_pressed, on_no_btn_pressed):
        self.on_yes_btn_pressed = on_yes_btn_pressed
        self.on_no_btn_pressed = on_no_btn_pressed
        self.yes_btn.on_widget_event = self.on_yes_btn_pressed
        self.no_btn.on_widget_event = self.on_no_btn_pressed

class MessagePopup(Container):
    def __init__(self, rect=[0, 0, 300, 200], radius=0, border_color="#000000", on_ok_pressed=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.on_ok_pressed = on_ok_pressed
        self.message_text = Label(
            [20, 30, 260, 100],
            data="",
        )
        self.message_text.alignment = "center"
        self.message_text.font_weight = "bold"
        self.message_text.font_size = 20

        self.ok_btn = Button(
            [107.5, 130, 85, 48]
        )
        self.ok_btn.label = "OK"
        self.ok_btn.on_widget_event = self.on_ok_pressed

        self.add_children([self.message_text, self.ok_btn])

    def set_ok_btn_callback(self, callback):
        self.on_ok_pressed = callback
        self.ok_btn.on_widget_event = self.on_ok_pressed
