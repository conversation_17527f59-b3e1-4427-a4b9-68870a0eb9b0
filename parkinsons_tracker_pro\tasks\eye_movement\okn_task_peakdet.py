import os
import time
import pickle
import numpy as np
from base_task import BaseTask
from screens.common_screen_element import InstructionTitle, TaskTitle
from screens.eye_movement.okn_instruction_screens import (
    OknInstructionLabelScreen,
    OknInstructionVideoScreen,
)
from screens.eye_movement.okn_visualization_screen import (
    OknVisualizationPlotScreen,
    OknVisualizationStatScreen,
)
from task_data import instructions, instruction_videos
from scipy.signal import savgol_filter
from peakdetect import peakdet
import json
from cortic_platform.sdk.app_events import ExceptionTypes


class OknTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("okn", module, on_task_complete, task_params)
        self.iris_target_locations = task_params
        with open(
            os.path.dirname(os.path.realpath(__file__)) + "/parameters.json"
        ) as f:
            self.params = json.load(f)
        self.x_scale_right = self.params["frame_width"] / (
            (
                self.iris_target_locations[1][0]
                - self.iris_target_locations[0][0]
                + self.iris_target_locations[3][0]
                - self.iris_target_locations[2][0]
            )
            * self.params["eye_range_scale_factor"]
            / 2
        )
        self.x_scale_left = self.params["frame_width"] / (
            (
                self.iris_target_locations[1][2]
                - self.iris_target_locations[0][2]
                + self.iris_target_locations[3][2]
                - self.iris_target_locations[2][2]
            )
            * self.params["eye_range_scale_factor"]
            / 2
        )
        self.y_scale_right = self.params["frame_height"] / (
            (
                self.iris_target_locations[2][1]
                - self.iris_target_locations[0][1]
                + self.iris_target_locations[3][1]
                - self.iris_target_locations[1][1]
            )
            * self.params["eye_range_scale_factor"]
            / 2
        )
        self.y_scale_left = self.params["frame_height"] / (
            (
                self.iris_target_locations[2][3]
                - self.iris_target_locations[0][3]
                + self.iris_target_locations[3][3]
                - self.iris_target_locations[1][3]
            )
            * self.params["eye_range_scale_factor"]
            / 2
        )
        self.title = TaskTitle(
            [840, 32, 571, 42], self.module.name, self.module.icon)

        self.screens = [
            OknInstructionLabelScreen(
                [200, 150, 1520, 850],
                instructions[self.name][0],
                subtitle="Eye Movements",
                font_size=40,
                on_continue=self.next_screen,
            )
        ]
        subtitle = "1. Optokinetic Nystagmus"
        for i in range(1, len(instructions[self.name]) - 1):
            self.screens.append(
                OknInstructionLabelScreen(
                    [200, 150, 1520, 850],
                    instructions[self.name][i],
                    font_size=52,
                    subtitle=subtitle,
                    on_continue=self.next_screen,
                )
            )
            self.screens.append(
                OknInstructionVideoScreen(
                    [0, 0, 1920, 1080],
                    instruction_videos[self.name][i - 1]["url"],
                    on_continue=self.next_screen,
                    on_video_event=self.on_video_event,
                )
            )
            subtitle = None

        self.screens.append(
            OknVisualizationPlotScreen(
                [200, 150, 1520, 850], on_continue=self.next_screen
            )
        )
        self.screens.append(
            OknVisualizationStatScreen(
                [200, 150, 1520, 850], on_continue=self.next_screen
            )
        )

        self.video_started = False
        self.video_stopped = True
        self.start_collection_time = None
        self.stop_collection_time = None
        self.right_eye_locations = []
        self.left_eye_locations = []
        self.all_right_eye_locations = []
        self.all_left_eye_locations = []
        self.num_blinks = 0
        self.fit_window_size = 10
        self.fit_polynomial_order = 3
        self.count = 0
        self.start_time = time.time()

    def on_video_event(self, data):
        if data == "initialized":
            self.video_started = True
            self.video_stopped = False
            self.start_collection_time = time.time()
        if data == "stopped":
            self.video_started = False
            self.video_stopped = True
            self.stop_collection_time = time.time()
            record_name = self.screens[self.current_screen_index].record_name
            self.all_right_eye_locations.append(
                {
                    "start_time": self.start_collection_time,
                    "stop_time": self.stop_collection_time,
                    "right_eye_locations": self.right_eye_locations,
                    "num_blinks": self.num_blinks,
                }
            )
            self.all_left_eye_locations.append(
                {
                    "start_time": self.start_collection_time,
                    "stop_time": self.stop_collection_time,
                    "left_eye_locations": self.left_eye_locations,
                    "num_blinks": self.num_blinks,
                }
            )
            with open(
                os.path.dirname(os.path.realpath(__file__))
                + "/okn_data/"
                + record_name
                + ".pkl",
                "wb",
            ) as f:
                pickle.dump(
                    {
                        "start_time": self.start_collection_time,
                        "stop_time": self.stop_collection_time,
                        "right_eye_locations": self.right_eye_locations,
                        "left_eye_locations": self.left_eye_locations,
                        "num_blinks": self.num_blinks,
                    },
                    f,
                )
            self.right_eye_locations = []
            self.left_eye_locations = []
            self.num_blinks = 0
            # iris_tracking_config_task = IrisTrackingTask()
            # iris_tracking_config_task.input = {"reset_blink": True}
            # iris_tracking_config_task.send()

    def get_eye_x_data(self, okn_data, data_key, absolute=False):
        if absolute:
            eye_x = [abs(x["x"]) for x in okn_data[data_key]]
        else:
            eye_x = [x["x"] for x in okn_data[data_key]]
        total_time = okn_data["stop_time"] - okn_data["start_time"]
        time_interval = total_time / len(eye_x)
        data_range = np.arange(0, len(eye_x))
        data_range = data_range * time_interval
        return data_range, eye_x

    def get_eye_y_data(self, okn_data, data_key, absolute=False):
        if absolute:
            eye_y = [abs(y["y"]) for y in okn_data[data_key]]
        else:
            eye_y = [y["y"] for y in okn_data[data_key]]
        total_time = okn_data["stop_time"] - okn_data["start_time"]
        time_interval = total_time / len(eye_y)
        data_range = np.arange(0, len(eye_y))
        data_range = data_range * time_interval
        return data_range, eye_y

    def next_screen(self, data=None):
        if self.current_screen_index == 0:
            self.current_content.enable_waiting_icon()
            self.module.background.update_content(self.current_content)
            video_capture_task = OakdCaptureTask()
            video_capture_task.input = {"action": "camera_parameters"}
            video_capture_task.send()
            self.camera_params = video_capture_task.output.get_data()
            camera_matrix = np.array(self.camera_params["camera_matrix"])
            iris_tracking_config_task = IrisTrackingTask()
            iris_tracking_config_task.input = {
                "action": "config",
                "frame_width": self.camera_params["frame_width"],
                "frame_height": self.camera_params["frame_height"],
                "focal_length": (camera_matrix[0, 0] + camera_matrix[1, 1]) / 2,
                "camera_matrix": self.camera_params["camera_matrix"],
                "okn_mode": True,
            }
            iris_tracking_config_task.send()
            result = iris_tracking_config_task.output.get_data()
            print("Configure iris tracking service finished")
            self.current_content.disable_waiting_icon()
            self.module.background.update_content(self.current_content)
        self.current_screen_index += 1
        if self.current_screen_index >= len(self.screens):
            self.on_task_complete("okn")
        else:
            if isinstance(
                self.screens[self.current_screen_index], OknVisualizationPlotScreen
            ):
                self.screens[self.current_screen_index].initialize(
                    self.all_right_eye_locations,
                    self.all_left_eye_locations,
                    self.get_fitted_data(
                        self.all_right_eye_locations, self.all_left_eye_locations
                    ),
                )
            if isinstance(
                self.screens[self.current_screen_index], OknVisualizationStatScreen
            ):
                okn_stats = self.get_okn_stat(
                    self.all_right_eye_locations, self.all_left_eye_locations
                )
                self.screens[self.current_screen_index].initialize(okn_stats)
            self.current_content = self.screens[self.current_screen_index]
            self.module.background.update_content(self.current_content)
            if (
                type(self.screens[self.current_screen_index])
                == OknInstructionVideoScreen
            ):
                self.module.background.hide_footer()
            else:
                self.module.background.show_footer()

    def get_qp_sp_velocity(self, qp_direction, local_maximas, local_minimas):
        qp_velocity = 0
        sp_velocity = 0
        num_qp = 0
        num_sp = 0

        if qp_direction == "Positive":
            for i in range(len(local_minimas)):
                if i < len(local_maximas):
                    sp_velocity += abs(
                        (local_minimas[i][1] - local_maximas[i][1])
                        / (local_minimas[i][0] - local_maximas[i][0])
                    )
                    num_sp += 1
                if (i + 1) < len(local_maximas):
                    qp_velocity += abs(
                        (local_maximas[i + 1][1] - local_minimas[i][1])
                        / (local_maximas[i + 1][0] - local_minimas[i][0])
                    )
                    num_qp += 1
        else:
            current_maximas = local_maximas
            if local_maximas[0][0] < local_minimas[0][0]:
                current_maximas = current_maximas[1:]
            for i in range(len(current_maximas)):
                if i < len(local_minimas):
                    sp_velocity += abs(
                        (current_maximas[i][1] - local_minimas[i][1])
                        / (current_maximas[i][0] - local_minimas[i][0])
                    )
                    num_sp += 1
                if (i + 1) < len(local_minimas):
                    qp_velocity += abs(
                        (local_minimas[i + 1][1] - current_maximas[i][1])
                        / (local_minimas[i + 1][0] - current_maximas[i][0])
                    )
                    num_qp += 1
        if num_qp == 0:
            mean_qp = 0
        else:
            mean_qp = qp_velocity / num_qp

        if num_sp == 0:
            mean_sp = 0
        else:
            mean_sp = sp_velocity / num_sp

        return mean_qp, mean_sp

    def search_real_extremas(
        self,
        data_range,
        raw_data,
        qp_direction,
        local_maximas,
        local_minimas,
        local_maximas_derivative,
        local_minimas_derivative,
    ):
        real_local_maximas = []
        real_local_minimas = []

        # Refine local maximas
        for i in range(len(local_maximas)):
            start_idx = np.where(data_range == local_maximas[i][0])[0][0]
            if i == 0:
                slope = abs(
                    (local_maximas[i][1] - local_minimas[i][1])
                    / (local_maximas[i][0] - local_minimas[i][0])
                )
            else:
                slope = abs(
                    (local_maximas[i][1] - local_minimas[i - 1][1])
                    / (local_maximas[i][0] - local_minimas[i - 1][0])
                )
            max_start_time = (
                data_range[start_idx] -
                self.params["slope_scale_factor"] * slope
            )
            max_end_time = (
                data_range[start_idx] +
                self.params["slope_scale_factor"] * slope
            )
            if max_start_time < 0:
                max_start_time = 0
            if max_end_time > data_range[-1]:
                max_end_time = data_range[-1]
            max_start_idx = np.where(data_range <= max_start_time)[0][-1]
            max_end_idx = np.where(data_range <= max_end_time)[0][-1]
            if i == 0:
                if max_end_idx >= np.where(data_range == local_minimas[i][0])[0][0]:
                    max_end_idx = np.where(
                        data_range == local_minimas[i][0])[0][0]
            else:
                if (
                    max_start_idx
                    <= np.where(data_range == local_minimas[i - 1][0])[0][0]
                ):
                    max_start_idx = np.where(data_range == local_minimas[i - 1][0])[0][
                        0
                    ]
                if i < len(local_minimas):
                    if max_end_idx >= np.where(data_range == local_minimas[i][0])[0][0]:
                        max_end_idx = np.where(
                            data_range == local_minimas[i][0])[0][0]

            real_maxima_idx = max_start_idx + np.argmax(
                raw_data[max_start_idx:max_end_idx]
            )
            real_local_maximas.append(
                [data_range[real_maxima_idx], raw_data[real_maxima_idx]]
            )

        # Refine local minimas
        for i in range(len(local_minimas)):
            start_idx = np.where(data_range == local_minimas[i][0])[0][0]
            if i < len(local_maximas):
                slope = abs(
                    (local_minimas[i][1] - local_maximas[i][1])
                    / (local_minimas[i][0] - local_maximas[i][0])
                )
            mini_start_time = (
                data_range[start_idx] -
                self.params["slope_scale_factor"] * slope
            )
            mini_end_time = (
                data_range[start_idx] +
                self.params["slope_scale_factor"] * slope
            )
            if mini_start_time < 0:
                mini_start_time = 0
            if mini_end_time > data_range[-1]:
                mini_end_time = data_range[-1]
            min_start_idx = np.where(data_range <= mini_start_time)[0][-1]
            min_end_idx = np.where(data_range <= mini_end_time)[0][-1]
            if min_start_idx <= np.where(data_range == local_maximas[i][0])[0][0]:
                min_start_idx = np.where(
                    data_range == local_maximas[i][0])[0][0]
            if (i + 1) < len(local_maximas):
                if min_end_idx >= np.where(data_range == local_maximas[i + 1][0])[0][0]:
                    min_end_idx = np.where(
                        data_range == local_maximas[i + 1][0])[0][0]
            real_minima_idx = min_start_idx + np.argmin(
                raw_data[min_start_idx:min_end_idx]
            )
            real_local_minimas.append(
                [data_range[real_minima_idx], raw_data[real_minima_idx]]
            )

        # Calculate average maxima and minima value for matching boundary points
        average_maxima_value = 0
        average_minima_value = 0
        if len(real_local_maximas) > 0:
            average_maxima_value = np.mean(real_local_maximas, axis=0)[1]
        if len(real_local_minimas) > 0:
            average_minima_value = np.mean(real_local_minimas, axis=0)[1]

        # Check start boundary
        if local_minimas_derivative[0][0] < local_maximas[0][0]:
            start_idx = 0
            end_idx = np.where(data_range == local_maximas[0][0])[0][0]
            best_minima_idx_within_range = -1
            best_minima_value_within_range = 999
            for i in range(start_idx, end_idx):
                if (
                    abs(raw_data[i] - average_minima_value) /
                        abs(average_minima_value)
                    <= self.params["boundary_extrema_threshold"]
                ):
                    if raw_data[i] < best_minima_value_within_range:
                        best_minima_idx_within_range = i
                        best_minima_value_within_range = raw_data[i]
            if best_minima_idx_within_range != -1:
                real_local_minimas.insert(
                    0,
                    [
                        data_range[best_minima_idx_within_range],
                        best_minima_value_within_range,
                    ],
                )

        # Check end boundary

        if local_maximas[-1][0] > local_minimas[-1][0]:
            start_idx = np.where(data_range == local_maximas[-1][0])[0][0]
            best_minima_idx_within_range = -1
            best_minima_value_within_range = 999
            for i in range(start_idx, len(raw_data)):
                if (
                    abs(raw_data[i] - average_minima_value) /
                        abs(average_minima_value)
                    <= self.params["boundary_extrema_threshold"]
                ):
                    if raw_data[i] < best_minima_value_within_range:
                        best_minima_idx_within_range = i
                        best_minima_value_within_range = raw_data[i]
            if best_minima_idx_within_range != -1:
                real_local_minimas.append(
                    [
                        data_range[best_minima_idx_within_range],
                        best_minima_value_within_range,
                    ]
                )
        else:
            start_idx = np.where(data_range == local_minimas[-1][0])[0][0]
            best_maxima_idx_within_range = -1
            best_maxima_value_within_range = -999
            for i in range(start_idx, len(raw_data)):
                if (
                    abs(raw_data[i] - average_maxima_value) /
                        abs(average_maxima_value)
                    <= self.params["boundary_extrema_threshold"]
                ):
                    if raw_data[i] > best_maxima_value_within_range:
                        best_maxima_idx_within_range = i
                        best_maxima_value_within_range = raw_data[i]
            if best_maxima_idx_within_range != -1:
                real_local_maximas.append(
                    [
                        data_range[best_maxima_idx_within_range],
                        best_maxima_value_within_range,
                    ]
                )

        if real_local_maximas[-1][0] > real_local_minimas[-1][0]:
            if local_minimas_derivative[-1][0] > local_maximas[-1][0]:
                start_idx = np.where(data_range == local_maximas[-1][0])[0][0]
                best_minima_idx_within_range = -1
                best_minima_value_within_range = 999
                for i in range(start_idx, len(raw_data)):
                    if raw_data[i] < best_minima_value_within_range:
                        best_minima_idx_within_range = i
                        best_minima_value_within_range = raw_data[i]
                if best_minima_idx_within_range != -1:
                    real_local_minimas.append(
                        [
                            data_range[best_minima_idx_within_range],
                            best_minima_value_within_range,
                        ],
                    )

        mean_qp, mean_sp = self.get_qp_sp_velocity(
            qp_direction, real_local_maximas, real_local_minimas
        )

        return (
            np.array(real_local_maximas),
            np.array(real_local_minimas),
            mean_qp,
            mean_sp,
        )

    def get_qp_direction(self, fitted_data, data_range, peak_threshold=2):
        qp_direction = ""
        data_derivate = np.diff(fitted_data)
        fitted_data_derivate = data_derivate.copy()
        if self.params["derivative_smoothing"]:
            fitted_data_derivate = savgol_filter(
                data_derivate,
                self.params["derivative_fit_window_size"],
                self.params["derivative_fit_polynomial_order"],
            )
        derivated_data_range = data_range[: len(data_range) - 1]
        local_maximas_derivative, local_minimas_derivative = peakdet(
            fitted_data_derivate, peak_threshold, x=derivated_data_range
        )

        local_maxima_idxs = []
        local_minima_idxs = []

        for value in local_maximas_derivative[:, 0]:
            local_maxima_idxs.append(np.where(data_range == value))

        for value in local_minimas_derivative[:, 0]:
            local_minima_idxs.append(np.where(data_range == value))

        sum = 0

        for idx in local_maxima_idxs:
            sum += data_derivate[idx]

        for idx in local_minima_idxs:
            sum += data_derivate[idx]

        if sum > 0:
            qp_direction = "Positive"

        if sum < 0:
            qp_direction = "Negative"

        return (
            qp_direction,
            derivated_data_range,
            fitted_data_derivate,
            local_maximas_derivative,
            local_minimas_derivative,
        )

    def get_fitted_data(self, right_datas, left_datas):
        results = []
        for i in range(len(right_datas)):
            right_data = right_datas[i]
            left_data = left_datas[i]
            if i < 2:
                right_data_range, right_raw_data = self.get_eye_x_data(
                    right_data, "right_eye_locations"
                )
                left_data_range, left_raw_data = self.get_eye_x_data(
                    left_data, "left_eye_locations"
                )
                (
                    right_filtered_data,
                    right_local_maximas,
                    right_local_minimas,
                    right_qp_direction,
                    right_qp_velocity,
                    right_sp_velocity,
                ) = self.fit_data(
                    right_data_range,
                    right_raw_data,
                    peak_threshold=self.params["x_extrema_threshold"],
                    qp_peak_threshold=self.params["x_peak_threshold"],
                )
                (
                    left_filtered_data,
                    left_local_maximas,
                    left_local_minimas,
                    left_qp_direction,
                    left_qp_velocity,
                    left_sp_velocity,
                ) = self.fit_data(
                    left_data_range,
                    left_raw_data,
                    peak_threshold=self.params["x_extrema_threshold"],
                    qp_peak_threshold=self.params["x_peak_threshold"],
                )
            else:
                right_data_range, right_raw_data = self.get_eye_y_data(
                    right_data, "right_eye_locations"
                )
                left_data_range, left_raw_data = self.get_eye_y_data(
                    left_data, "left_eye_locations"
                )
                (
                    right_filtered_data,
                    right_local_maximas,
                    right_local_minimas,
                    right_qp_direction,
                    right_qp_velocity,
                    right_sp_velocity,
                ) = self.fit_data(
                    right_data_range,
                    right_raw_data,
                    peak_threshold=self.params["y_extrema_threshold"],
                    qp_peak_threshold=self.params["y_peak_threshold"],
                )
                (
                    left_filtered_data,
                    left_local_maximas,
                    left_local_minimas,
                    left_qp_direction,
                    left_qp_velocity,
                    left_sp_velocity,
                ) = self.fit_data(
                    left_data_range,
                    left_raw_data,
                    peak_threshold=self.params["y_extrema_threshold"],
                    qp_peak_threshold=self.params["y_peak_threshold"],
                )

            results.append(
                {
                    "right_filtered_data_range": right_data_range,
                    "right_filtered_data": right_filtered_data,
                    "left_filtered_data_range": left_data_range,
                    "left_filtered_data": left_filtered_data,
                    "right_local_maximas": right_local_maximas,
                    "right_local_minimas": right_local_minimas,
                    "left_local_maximas": left_local_maximas,
                    "left_local_minimas": left_local_minimas,
                }
            )
        return results

    def fit_data(self, data_range, raw_data, peak_threshold=1, qp_peak_threshold=1):
        filtered_data = savgol_filter(
            raw_data,
            self.params["fit_window_size"],
            self.params["fit_polynomial_order"],
        )
        fit_maximas, fit_minimas = peakdet(
            filtered_data, peak_threshold, x=data_range)
        # local_maximas, local_minimas = self.search_real_extremas(
        #     data_range, raw_data, fit_maximas, fit_minimas
        # )

        (
            qp_direction,
            derivated_data_range,
            fitted_data_derivate,
            local_maximas_derivative,
            local_minimas_derivative,
        ) = self.get_qp_direction(
            filtered_data, data_range, peak_threshold=qp_peak_threshold
        )

        local_maximas, local_minimas, qp_speed, sp_speed = self.search_real_extremas(
            data_range,
            raw_data,
            qp_direction,
            fit_maximas,
            fit_minimas,
            local_maximas_derivative,
            local_minimas_derivative,
        )
        return (
            filtered_data,
            local_maximas,
            local_minimas,
            qp_direction,
            qp_speed,
            sp_speed,
        )

    def calculate_nystagmus_time(self, local_maximas, local_minimas):
        start_time = local_maximas[0][0]
        end_time = local_maximas[-1][0]
        if local_maximas[0][0] > local_minimas[0][0]:
            start_time = local_minimas[0][0]
        if local_maximas[-1][0] < local_minimas[-1][0]:
            end_time = local_minimas[-1][0]
        return end_time - start_time

    def compute_okn_stat(
        self, data, key, target_veolcity, peak_threshold=1, get_x=True
    ):
        result = {"num_blinks": data["num_blinks"]}
        if get_x:
            data_range, raw_data = self.get_eye_x_data(data, key)
        else:
            data_range, raw_data = self.get_eye_y_data(data, key)

        if get_x:
            (
                filtered_data,
                local_maximas,
                local_minimas,
                qp_direction,
                qp_velocity,
                sp_velocity,
            ) = self.fit_data(
                data_range,
                raw_data,
                peak_threshold=peak_threshold,
                qp_peak_threshold=self.params["x_peak_threshold"],
            )
        else:
            (
                filtered_data,
                local_maximas,
                local_minimas,
                qp_direction,
                qp_velocity,
                sp_velocity,
            ) = self.fit_data(
                data_range,
                raw_data,
                peak_threshold=peak_threshold,
                qp_peak_threshold=self.params["y_peak_threshold"],
            )

        result["time_nystagmus"] = self.calculate_nystagmus_time(
            local_maximas, local_minimas
        ) / np.max(data_range)

        if qp_direction == "Negative":
            result["nystagmus_latency"] = local_minimas[0][0]
            result["num_nystagmus"] = len(local_minimas) - 1
        else:
            result["nystagmus_latency"] = local_maximas[0][0]
            result["num_nystagmus"] = len(local_maximas) - 1

        result["qp_direction"] = qp_direction
        result["qp_velocity"] = qp_velocity
        result["sp_velocity"] = sp_velocity
        result["sp_velocity_gain"] = sp_velocity / target_veolcity
        return result

    def get_okn_stat(self, right_datas, left_datas):
        results = []
        for i in range(len(right_datas)):
            right_data = right_datas[i]
            left_data = left_datas[i]
            if i < 2:
                results.append(
                    {
                        "right": self.compute_okn_stat(
                            right_data,
                            "right_eye_locations",
                            instruction_videos[self.name][i]["target_velocity"],
                            peak_threshold=self.params["x_extrema_threshold"],
                        ),
                        "left": self.compute_okn_stat(
                            left_data,
                            "left_eye_locations",
                            instruction_videos[self.name][i]["target_velocity"],
                            peak_threshold=self.params["x_extrema_threshold"],
                        ),
                    }
                )
            else:
                results.append(
                    {
                        "right": self.compute_okn_stat(
                            right_data,
                            "right_eye_locations",
                            instruction_videos[self.name][i]["target_velocity"],
                            peak_threshold=self.params["y_extrema_threshold"],
                            get_x=False,
                        ),
                        "left": self.compute_okn_stat(
                            left_data,
                            "left_eye_locations",
                            instruction_videos[self.name][i]["target_velocity"],
                            peak_threshold=self.params["y_extrema_threshold"],
                            get_x=False,
                        ),
                    }
                )
        return results

    def process(self):
        if isinstance(self.current_content, OknInstructionVideoScreen):
            if self.video_started:
                self.count += 1
                if time.time() - self.start_time >= 1:
                    print("FPS:", self.count)
                    self.count = 0
                    self.start_time = time.time()
                video_capture_task = OakdCaptureTask()
                video_capture_task.input = {
                    "record": self.current_content.record_name}
                video_capture_task.send()
                iris_tracking_task = IrisTrackingTask()
                iris_tracking_task.input = video_capture_task.output
                iris_tracking_task.send()
                iris_tracking_result = iris_tracking_task.output.get_data()
                
                self.right_eye_locations.append(
                    {
                        "x": iris_tracking_result["right_iris_location"][0]
                        * self.camera_params["frame_width"],
                        "y": iris_tracking_result["right_iris_location"][1]
                        * self.camera_params["frame_height"],
                    }
                )
                self.left_eye_locations.append(
                    {
                        "x": iris_tracking_result["left_iris_location"][0]
                        * self.camera_params["frame_width"],
                        "y": iris_tracking_result["left_iris_location"][1]
                        * self.camera_params["frame_height"],
                    }
                )
                self.num_blinks = iris_tracking_result["num_blinks"]
