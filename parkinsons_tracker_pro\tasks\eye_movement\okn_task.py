import os
import time
import numpy as np
from base_task import BaseTask
from screens.common_screen_element import TaskTitle
from screens.text_instruction_screen import TextInstructionScreen
from screens.eye_movement.okn_instruction_screens import (
    OknInstructionVideoScreen,
)
from task_data import instructions, instruction_videos
from scipy.signal import savgol_filter
import json
import sys
import statistics
from service_registry import *
from cortic_platform.sdk.app_events import ExceptionTypes
from task_data import messages
import traceback


class OknTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("okn", module, on_task_complete, task_params)
        self.iris_target_locations = task_params
        with open(
            os.path.dirname(os.path.realpath(__file__)) + "/parameters.json"
        ) as f:
            self.params = json.load(f)
        self.temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/temp_collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/okn/"
        )
        self.collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/okn/"
        )
        os.makedirs(os.path.dirname(
            self.temp_collected_data_path), exist_ok=True)

        self.title = TaskTitle(
            [840, 32, 571, 42], self.module.name, self.module.icon)

        self.screens = [
            TextInstructionScreen(
                [200, 90, 1520, 950],
                instruction_text=instructions[self.name][0],
                subtitle="Eye Movements",
                font_size=45,
                on_continue=self.next_screen,
            ),
        ]
        subtitle = "Optokinetic Nystagmus"
        for i in range(1, len(instructions[self.name]) - 1):
            self.screens.append(
                TextInstructionScreen(
                    [200, 90, 1520, 950],
                    instruction_text=instructions[self.name][i],
                    subtitle=subtitle,
                    font_size=45,
                    on_continue=self.next_screen,
                )
            )
            self.screens.append(
                OknInstructionVideoScreen(
                    [0, 0, 1920, 1080],
                    self.module.resources[
                        instruction_videos[self.name][i - 1]["filename"]
                    ],
                    on_continue=self.next_screen,
                    on_redo=self.redo_task,
                    on_video_event=self.on_video_event,
                )
            )
            subtitle = None

        self.video_started = False
        self.video_stopped = True
        self.start_collection_time = None
        self.stop_collection_time = None
        self.right_eye_locations = []
        self.left_eye_locations = []
        self.all_right_eye_locations = []
        self.all_left_eye_locations = []
        self.num_blinks = 0
        self.fit_window_size = 10
        self.fit_polynomial_order = 3
        self.camera_params = {"frame_width": 1280, "frame_height": 720}
        self.module.session.logger.info(
            "("
            + self.module.session.patient_id
            + ") "
            + self.name
            + " task initialized"
        )

    def on_video_event(self, data):
        # return Ture if event is handled correctly
        # return False if there is exception
        if data == "initialized":
            self.video_started = True
            self.video_stopped = False
            self.start_collection_time = time.time()
            self.module.session.logger.info(self.name + " task video started.")
        if data == "stopped":
            self.module.session.logger.info(self.name + " task video stopped.")
            self.video_started = False
            self.video_stopped = True
            self.camera_params = oakd_capture({"reset_video": True,
                                               "video_file_name": ""}).get_data()
            self.stop_collection_time = time.time()
            record_name = self.screens[self.current_screen_index].record_name
            self.all_right_eye_locations.append(
                {
                    "start_time": self.start_collection_time,
                    "stop_time": self.stop_collection_time,
                    "right_eye_locations": self.right_eye_locations,
                    "num_blinks": self.num_blinks,
                }
            )
            self.all_left_eye_locations.append(
                {
                    "start_time": self.start_collection_time,
                    "stop_time": self.stop_collection_time,
                    "left_eye_locations": self.left_eye_locations,
                    "num_blinks": self.num_blinks,
                }
            )
            filename = self.temp_collected_data_path + record_name + "_raw.json"
            os.makedirs(os.path.dirname(
                self.temp_collected_data_path), exist_ok=True)
            with open(
                filename,
                "w+",
            ) as f:
                f.write(
                    json.dumps(
                        {
                            "start_time": self.start_collection_time,
                            "stop_time": self.stop_collection_time,
                            "right_eye_locations": self.right_eye_locations,
                            "left_eye_locations": self.left_eye_locations,
                            "num_blinks": self.num_blinks,
                        }
                    )
                )
            self.module.session.logger.info(
                self.name
                + " task: saved "
                + self.temp_collected_data_path
                + record_name
                + "_raw.json"
            )
            self.right_eye_locations = []
            self.left_eye_locations = []
            self.num_blinks = 0
            self.module.session.logger.info(
                self.name
                + " task: video recording stopped for "
                + self.current_content.record_name
            )
            self.module.session.logger.info(
                self.name
                + " task: video is saved to "
                + self.temp_collected_data_path
                + " with name:"
                + self.current_content.record_name
                + ".h264"
            )
            file_size = 0
            try:
                file_size = str(
                    sys.getsizeof(
                        self.temp_collected_data_path
                        + self.current_content.record_name
                        + ".h264"
                    )
                )
            except:
                pass
            self.module.session.logger.info(
                self.name + " task: recorded video size is: " + file_size
            )
            frame_data = oakd_capture(
                {"reset_video": True, "video_file_name": ""})
            if frame_data is not None:
                iris_detection_result = iris_detection({"camera_input": frame_data},
                                           service_states={"reset_blink": True})
                if iris_detection_result and not isinstance(iris_detection_result, ExceptionTypes):
                    iris_detection_result.get_data()
            print("End of video stop event")
            self.module.session.logger.info(
                self.name
                + " task: checking if data: "
                + self.current_content.record_name
                + " is recorded..."
            )
            print("Checking if data is recorded")
            if not os.path.isfile(
                self.temp_collected_data_path
                + self.current_content.record_name
                + ".h264"
            ):
                self.module.session.logger.error(
                    "Video File:"
                    + self.current_content.record_name
                    + " for task "
                    + self.name
                    + " is not found, raising exception"
                )
                self.module.session.logger.error(
                    self.name
                    + " task recorded data:"
                    + self.current_content.record_name
                    + " not found"
                )
                sys.stderr.write(
                    json.dumps(
                        {
                            "app_exception_msg": self.name
                            + ": "
                            + messages["recordedDataNotFound"]
                        }
                    )
                    + "\n"
                )
                return False
            else:
                self.module.session.logger.info(
                    self.name
                    + " task recorded data:"
                    + self.current_content.record_name
                    + " is found"
                )
        return True

    def get_eye_x_data(self, okn_data, data_key, absolute=False):
        eye_x = [x["x"] for x in okn_data[data_key]]
        total_time = okn_data["stop_time"] - okn_data["start_time"]
        time_interval = total_time / len(eye_x)
        data_range = np.arange(0, len(eye_x))
        data_range = data_range * time_interval
        return data_range, eye_x

    def get_eye_y_data(self, okn_data, data_key, absolute=False):
        eye_y = [y["y"] for y in okn_data[data_key]]
        if self.params["flip_y_axis"]:
            eye_y = np.array(eye_y) * -1
        total_time = okn_data["stop_time"] - okn_data["start_time"]
        time_interval = total_time / len(eye_y)
        data_range = np.arange(0, len(eye_y))
        data_range = data_range * time_interval
        return data_range, eye_y

    def redo_task(self, data=None):
        if (self.current_screen_index == 0):
            return
        self.current_screen_index -= 1
        print("Redo current task, updating to previous instruction screen")
        self.current_content = self.screens[self.current_screen_index]
        self.module.session.update_current_task_screen(self.current_content)
        print("Updated to instruction screen")

    def next_screen(self, data=None):
        if self.current_screen_index == 0:
            self.module.session.loading_background.visible = True
            self.module.session.widget_tree.update(
                self.module.session.loading_background
            )
            self.module.session.loading_background.visible = False
            self.module.session.widget_tree.update(
                self.module.session.loading_background
            )
            self.module.session.update_current_task_screen(self.current_content)
        self.current_screen_index += 1
        if self.current_screen_index >= len(self.screens):
            self.on_task_complete("okn")
        else:
            print("Updating to next screen")
            self.current_content = self.screens[self.current_screen_index]
            self.module.session.update_current_task_screen(self.current_content)
            print("Updated to next screen")
            # if (
            #     type(self.screens[self.current_screen_index])
            #     == OknInstructionVideoScreen
            # ):
            #     self.module.background.hide_footer()
            # else:
            #     self.module.background.show_footer()
            print("End of next screen function call")

    def get_qp_sp_velocity(self, local_maximas, local_minimas):
        qp_velocity = 0
        sp_velocity = 0
        left_side_velocity = []
        right_side_velocity = []
        left_side_times = []
        right_side_times = []
        qp_direction = "Positive"
        first_extream_is_minima = False
        if local_maximas[0][0] > local_minimas[0][0]:
            first_extream_is_minima = True

        if first_extream_is_minima:
            for i in range(len(local_minimas)):
                if i < len(local_maximas):
                    current_velocity = abs(
                        (local_maximas[i][1] - local_minimas[i][1])
                        / (local_maximas[i][0] - local_minimas[i][0])
                    )
                    left_side_velocity.append(current_velocity)
                    left_side_times.append(
                        abs(local_maximas[i][0] - local_minimas[i][0])
                    )
                if i > 0:
                    if i - 1 < len(local_maximas):
                        current_velocity = abs(
                            (local_minimas[i][1] - local_maximas[i - 1][1])
                            / (local_minimas[i][0] - local_maximas[i - 1][0])
                        )
                        right_side_velocity.append(current_velocity)
                        right_side_times.append(
                            abs(local_minimas[i][0] - local_maximas[i - 1][0])
                        )
        else:
            for i in range(len(local_maximas)):
                if i < len(local_minimas):
                    current_velocity = abs(
                        (local_minimas[i][1] - local_maximas[i][1])
                        / (local_minimas[i][0] - local_maximas[i][0])
                    )
                    right_side_velocity.append(current_velocity)
                    right_side_times.append(
                        abs(local_minimas[i][0] - local_maximas[i][0])
                    )
                if i > 0:
                    if i - 1 < len(local_minimas):
                        current_velocity = abs(
                            (local_maximas[i][1] - local_minimas[i - 1][1])
                            / (local_maximas[i][0] - local_minimas[i - 1][0])
                        )
                        left_side_velocity.append(current_velocity)
                        left_side_times.append(
                            abs(local_maximas[i][0] - local_minimas[i - 1][0])
                        )

        # mean_left_velocity = left_side_velocity / num_left_side
        # mean_right_velocity = right_side_velocity / num_right_side
        mean_left_velocity = statistics.median(left_side_velocity)
        mean_right_velocity = statistics.median(right_side_velocity)
        mean_left_time = statistics.median(left_side_times)
        mean_right_time = statistics.median(right_side_times)

        if mean_left_velocity < mean_right_velocity:
            qp_direction = "Negative"
            qp_velocity = mean_right_velocity
            sp_velocity = mean_left_velocity
            qp_time = mean_right_time
            sp_time = mean_left_time
        else:
            qp_velocity = mean_left_velocity
            sp_velocity = mean_right_velocity
            qp_time = mean_left_time
            sp_time = mean_right_time

        return qp_velocity, sp_velocity, qp_time, sp_time, qp_direction

    def refine_local_extremas(
        self,
        data_range,
        raw_data,
        local_maximas,
        local_minimas,
    ):
        real_local_maximas = []
        real_local_minimas = []

        # Determine what is the first extrema
        first_extream_is_minima = False
        if local_maximas[0][0] > local_minimas[0][0]:
            first_extream_is_minima = True

        # Refine local maximas
        for i in range(len(local_maximas)):
            start_idx = np.where(data_range == local_maximas[i][0])[0][0]
            if i == 0:
                slope = abs(
                    (local_maximas[i][1] - local_minimas[i][1])
                    / (local_maximas[i][0] - local_minimas[i][0])
                )
            else:
                if first_extream_is_minima:
                    slope = abs(
                        (local_maximas[i][1] - local_minimas[i][1])
                        / (local_maximas[i][0] - local_minimas[i][0])
                    )
                else:
                    slope = abs(
                        (local_maximas[i][1] - local_minimas[i - 1][1])
                        / (local_maximas[i][0] - local_minimas[i - 1][0])
                    )

            max_start_time = (
                data_range[start_idx] -
                self.params["slope_scale_factor"] * slope
            )
            max_end_time = (
                data_range[start_idx] +
                self.params["slope_scale_factor"] * slope
            )
            if max_start_time < 0:
                max_start_time = 0
            if max_end_time > data_range[-1]:
                max_end_time = data_range[-1]
            max_start_idx = np.where(data_range <= max_start_time)[0][-1]
            max_end_idx = np.where(data_range <= max_end_time)[0][-1]
            if i == 0:
                if first_extream_is_minima:
                    if (
                        max_start_idx
                        <= np.where(data_range == local_minimas[i][0])[0][0]
                    ):
                        max_start_idx = np.where(data_range == local_minimas[i][0])[0][
                            0
                        ]
                    if (
                        max_end_idx
                        >= np.where(data_range == local_minimas[i + 1][0])[0][0]
                    ):
                        max_end_idx = np.where(data_range == local_minimas[i + 1][0])[
                            0
                        ][0]
                else:
                    if max_end_idx >= np.where(data_range == local_minimas[i][0])[0][0]:
                        max_end_idx = np.where(
                            data_range == local_minimas[i][0])[0][0]
            else:
                if first_extream_is_minima:
                    if (
                        max_start_idx
                        <= np.where(data_range == local_minimas[i][0])[0][0]
                    ):
                        max_start_idx = np.where(data_range == local_minimas[i][0])[0][
                            0
                        ]
                    if i + 1 < len(local_minimas):
                        if (
                            max_end_idx
                            >= np.where(data_range == local_minimas[i + 1][0])[0][0]
                        ):
                            max_end_idx = np.where(
                                data_range == local_minimas[i + 1][0]
                            )[0][0]
                else:
                    if (
                        max_start_idx
                        <= np.where(data_range == local_minimas[i - 1][0])[0][0]
                    ):
                        max_start_idx = np.where(data_range == local_minimas[i - 1][0])[
                            0
                        ][0]
                    if i < len(local_minimas):
                        if (
                            max_end_idx
                            >= np.where(data_range == local_minimas[i][0])[0][0]
                        ):
                            max_end_idx = np.where(data_range == local_minimas[i][0])[
                                0
                            ][0]
            data_segment = raw_data[max_start_idx:max_end_idx]
            if len(data_segment) > 0:
                real_maxima_idx = max_start_idx + np.argmax(data_segment)
                real_local_maximas.append(
                    [data_range[real_maxima_idx], raw_data[real_maxima_idx]]
                )

        # Refine local minimas
        for i in range(len(local_minimas)):
            start_idx = np.where(data_range == local_minimas[i][0])[0][0]
            if i == 0:
                slope = abs(
                    (local_minimas[i][1] - local_maximas[i][1])
                    / (local_minimas[i][0] - local_maximas[i][0])
                )
            else:
                if first_extream_is_minima:
                    slope = abs(
                        (local_minimas[i][1] - local_maximas[i - 1][1])
                        / (local_minimas[i][0] - local_maximas[i - 1][0])
                    )
                else:
                    slope = abs(
                        (local_maximas[i][1] - local_minimas[i][1])
                        / (local_maximas[i][0] - local_minimas[i][0])
                    )
            mini_start_time = (
                data_range[start_idx] -
                self.params["slope_scale_factor"] * slope
            )
            mini_end_time = (
                data_range[start_idx] +
                self.params["slope_scale_factor"] * slope
            )
            if mini_start_time < 0:
                mini_start_time = 0
            if mini_end_time > data_range[-1]:
                mini_end_time = data_range[-1]
            min_start_idx = np.where(data_range <= mini_start_time)[0][-1]
            min_end_idx = np.where(data_range <= mini_end_time)[0][-1]
            if i == 0:
                if first_extream_is_minima:
                    if min_end_idx >= np.where(data_range == local_maximas[i][0])[0][0]:
                        min_end_idx = np.where(
                            data_range == local_maximas[i][0])[0][0]
                else:
                    if (
                        min_start_idx
                        <= np.where(data_range == local_maximas[i][0])[0][0]
                    ):
                        min_start_idx = np.where(data_range == local_maximas[i][0])[0][
                            0
                        ]
                    if (
                        min_end_idx
                        >= np.where(data_range == local_maximas[i + 1][0])[0][0]
                    ):
                        min_end_idx = np.where(data_range == local_maximas[i + 1][0])[
                            0
                        ][0]
            else:
                if first_extream_is_minima:
                    if (
                        min_start_idx
                        <= np.where(data_range == local_maximas[i - 1][0])[0][0]
                    ):
                        min_start_idx = np.where(data_range == local_maximas[i - 1][0])[
                            0
                        ][0]
                    if i < len(local_maximas):
                        if (
                            min_end_idx
                            >= np.where(data_range == local_maximas[i][0])[0][0]
                        ):
                            min_end_idx = np.where(data_range == local_maximas[i][0])[
                                0
                            ][0]
                else:
                    if (
                        min_start_idx
                        <= np.where(data_range == local_maximas[i][0])[0][0]
                    ):
                        min_start_idx = np.where(data_range == local_maximas[i][0])[0][
                            0
                        ]
                    if i + 1 < len(local_maximas):
                        if (
                            min_end_idx
                            >= np.where(data_range == local_maximas[i + 1][0])[0][0]
                        ):
                            min_end_idx = np.where(
                                data_range == local_maximas[i + 1][0]
                            )[0][0]
            data_segment = raw_data[min_start_idx:min_end_idx]
            if len(data_segment) > 0:
                real_minima_idx = min_start_idx + np.argmin(
                    raw_data[min_start_idx:min_end_idx]
                )
                real_local_minimas.append(
                    [data_range[real_minima_idx], raw_data[real_minima_idx]]
                )

        # Calculate average maxima and minima value for matching boundary points
        average_maxima_value = 0
        average_minima_value = 0
        if len(real_local_maximas) > 0:
            average_maxima_value = np.mean(real_local_maximas, axis=0)[1]
        if len(real_local_minimas) > 0:
            average_minima_value = np.mean(real_local_minimas, axis=0)[1]

        # Check start boundary
        if first_extream_is_minima:
            end_idx = np.where(data_range == real_local_minimas[0][0])[0][0]
            best_maxima_idx_within_range = -1
            best_maxima_value_within_range = -999
            for i in range(0, end_idx):
                if (
                    abs(raw_data[i] - average_maxima_value) /
                        abs(average_maxima_value)
                    <= self.params["boundary_extrema_threshold"]
                ):
                    if raw_data[i] > best_maxima_value_within_range:
                        best_maxima_idx_within_range = i
                        best_maxima_value_within_range = raw_data[i]
            if best_maxima_idx_within_range != -1:
                index_diff = abs(best_maxima_idx_within_range - end_idx)
                if index_diff >= self.params["extrema_verify_window_size"] // 2:
                    real_local_maximas.insert(
                        0,
                        [
                            data_range[best_maxima_idx_within_range],
                            best_maxima_value_within_range,
                        ],
                    )
        else:
            end_idx = np.where(data_range == real_local_maximas[0][0])[0][0]
            best_minima_idx_within_range = -1
            best_minima_value_within_range = 999
            for i in range(0, end_idx):
                if (
                    abs(raw_data[i] - average_minima_value) /
                        abs(average_minima_value)
                    <= self.params["boundary_extrema_threshold"]
                ):
                    if raw_data[i] < best_minima_value_within_range:
                        best_minima_idx_within_range = i
                        best_minima_value_within_range = raw_data[i]
            if best_minima_idx_within_range != -1:
                index_diff = abs(best_minima_idx_within_range - end_idx)
                if index_diff >= self.params["extrema_verify_window_size"] // 2:
                    real_local_minimas.insert(
                        0,
                        [
                            data_range[best_minima_idx_within_range],
                            best_minima_value_within_range,
                        ],
                    )

        # Check end boundary
        if real_local_maximas[-1][0] > real_local_minimas[-1][0]:
            start_idx = np.where(data_range == real_local_maximas[-1][0])[0][0]
            best_minima_idx_within_range = -1
            best_minima_value_within_range = 999
            for i in range(start_idx, len(raw_data)):
                if (
                    abs(raw_data[i] - average_minima_value) /
                        abs(average_minima_value)
                    <= self.params["boundary_extrema_threshold"]
                ):
                    if raw_data[i] < best_minima_value_within_range:
                        best_minima_idx_within_range = i
                        best_minima_value_within_range = raw_data[i]
            if best_minima_idx_within_range != -1:
                index_diff = abs(best_minima_idx_within_range - start_idx)
                if index_diff >= self.params["extrema_verify_window_size"] // 2:
                    real_local_minimas.append(
                        [
                            data_range[best_minima_idx_within_range],
                            best_minima_value_within_range,
                        ]
                    )
        else:
            start_idx = np.where(data_range == real_local_minimas[-1][0])[0][0]
            best_maxima_idx_within_range = -1
            best_maxima_value_within_range = -999
            for i in range(start_idx, len(raw_data)):
                print(
                    abs(raw_data[i] - average_maxima_value) /
                    abs(average_maxima_value)
                )
                if (
                    abs(raw_data[i] - average_maxima_value) /
                        abs(average_maxima_value)
                    <= self.params["boundary_extrema_threshold"]
                ):
                    if raw_data[i] > best_maxima_value_within_range:
                        best_maxima_idx_within_range = i
                        best_maxima_value_within_range = raw_data[i]
            if best_maxima_idx_within_range != -1:
                index_diff = abs(best_maxima_idx_within_range - start_idx)
                if index_diff >= self.params["extrema_verify_window_size"] // 3:
                    real_local_maximas.append(
                        [
                            data_range[best_maxima_idx_within_range],
                            best_maxima_value_within_range,
                        ]
                    )

        (
            mean_qp,
            mean_sp,
            mean_qp_time,
            mena_sp_time,
            qp_direction,
        ) = self.get_qp_sp_velocity(real_local_maximas, real_local_minimas)

        if qp_direction == "Negative":
            # Check if a SP start extrema is missing
            if real_local_maximas[0][0] < real_local_minimas[0][0]:
                end_idx = np.where(
                    data_range == real_local_maximas[0][0])[0][0]
                best_minima_idx_within_range = -1
                best_minima_value_within_range = 999
                for i in range(end_idx):
                    if raw_data[i] < best_minima_value_within_range:
                        best_minima_idx_within_range = i
                        best_minima_value_within_range = raw_data[i]
                if best_minima_idx_within_range != -1:
                    index_diff = abs(best_minima_idx_within_range - end_idx)
                    if index_diff >= self.params["extrema_verify_window_size"]:
                        real_local_minimas.insert(
                            0,
                            [
                                data_range[best_minima_idx_within_range],
                                best_minima_value_within_range,
                            ],
                        )

        if qp_direction == "Positive":
            if real_local_minimas[0][0] < real_local_maximas[0][0]:
                real_local_minimas.pop(0)
        else:
            if real_local_maximas[0][0] < real_local_minimas[0][0]:
                real_local_maximas.pop(0)

        return (
            np.array(real_local_maximas),
            np.array(real_local_minimas),
            qp_direction,
            mean_qp,
            mean_sp,
            mean_qp_time,
            mena_sp_time,
        )

    def get_initial_extremas(self, data_range, fitted_data):
        fitted_extremas = []
        local_maximas = []
        local_minimas = []
        refined_maximas = []
        refined_minimas = []
        fitted_derivative = np.gradient(fitted_data)
        asign = np.sign(fitted_derivative)
        sz = asign == 0
        while sz.any():
            asign[sz] = np.roll(asign, 1)[sz]
            sz = asign == 0
        signchange = ((np.roll(asign, 1) - asign) != 0).astype(int)
        for i in range(len(signchange)):
            if signchange[i]:
                fitted_extremas.append([data_range[i], fitted_data[i]])
        for i in range(len(fitted_extremas)):
            extrema_idx = np.where(data_range == fitted_extremas[i][0])[0][0]
            start_idx = extrema_idx - self.params["extrema_verify_window_size"]
            end_idx = extrema_idx + self.params["extrema_verify_window_size"]
            if start_idx < 0:
                start_idx = 0
            if end_idx >= len(fitted_data):
                end_idx = len(fitted_data) - 1
            search_legnth = end_idx + 1 - start_idx
            max_idx = np.argmax(fitted_data[start_idx: end_idx + 1])
            min_idx = np.argmin(fitted_data[start_idx: end_idx + 1])
            max_left_right_ratio = 1
            if max_idx != 0:
                max_left_right_ratio = abs(
                    (search_legnth - max_idx) / max_idx - 1)
            min_left_right_ratio = 1
            if min_idx != 0:
                min_left_right_ratio = abs(
                    (search_legnth - min_idx) / min_idx - 1)
            if max_left_right_ratio < min_left_right_ratio:
                local_maximas.append(
                    [data_range[start_idx + max_idx],
                        fitted_data[start_idx + max_idx]]
                )
            else:
                local_minimas.append(
                    [data_range[start_idx + min_idx],
                        fitted_data[start_idx + min_idx]]
                )
        # Refine local minimas
        if local_maximas[0][0] > 0:
            end_maxima_time = local_maximas[0][0]
            minima_times = np.array(local_minimas)[:, 0]
            minimas_in_range = np.where(
                np.logical_and(minima_times >= 0,
                               minima_times <= end_maxima_time)
            )[0]
            if len(minimas_in_range) > 0:
                if len(minimas_in_range) == 1:
                    refined_minimas.append(np.array(local_minimas)[
                                           minimas_in_range][0])
                else:
                    minimas = np.array(local_minimas)[minimas_in_range]
                    true_minima = minimas[np.argmin(minimas[:, 1])]
                    refined_minimas.append(true_minima)

        for i in range(len(local_maximas) - 1):
            start_maxima_time = local_maximas[i][0]
            end_maxima_time = local_maximas[i + 1][0]
            minima_times = np.array(local_minimas)[:, 0]
            minimas_in_range = np.where(
                np.logical_and(
                    minima_times >= start_maxima_time, minima_times <= end_maxima_time
                )
            )[0]
            if len(minimas_in_range) > 0:
                if len(minimas_in_range) == 1:
                    refined_minimas.append(np.array(local_minimas)[
                                           minimas_in_range][0])
                else:
                    minimas = np.array(local_minimas)[minimas_in_range]
                    true_minima = minimas[np.argmin(minimas[:, 1])]
                    refined_minimas.append(true_minima)

        if local_maximas[-1][0] < data_range[-1]:
            start_maxima_time = local_maximas[-1][0]
            minima_times = np.array(local_minimas)[:, 0]
            minimas_in_range = np.where(
                np.logical_and(
                    minima_times >= start_maxima_time, minima_times <= data_range[-1]
                )
            )[0]
            if len(minimas_in_range) > 0:
                if len(minimas_in_range) == 1:
                    refined_minimas.append(np.array(local_minimas)[
                                           minimas_in_range][0])
                else:
                    minimas = np.array(local_minimas)[minimas_in_range]
                    true_minima = minimas[np.argmin(minimas[:, 1])]
                    refined_minimas.append(true_minima)

        # Refine local maximas
        if local_minimas[0][0] > 0:
            end_minima_time = local_minimas[0][0]
            maxima_times = np.array(local_maximas)[:, 0]
            maxima_in_range = np.where(
                np.logical_and(maxima_times >= 0,
                               maxima_times <= end_minima_time)
            )[0]
            if len(maxima_in_range) > 0:
                if len(maxima_in_range) == 1:
                    refined_maximas.append(np.array(local_maximas)[
                                           maxima_in_range][0])
                else:
                    maximas = np.array(local_maximas)[maxima_in_range]
                    true_maxima = maximas[np.argmax(maximas[:, 1])]
                    refined_maximas.append(true_maxima)

        for i in range(len(local_minimas) - 1):
            start_maxima_time = local_minimas[i][0]
            end_maxima_time = local_minimas[i + 1][0]
            maxima_times = np.array(local_maximas)[:, 0]
            maxima_in_range = np.where(
                np.logical_and(
                    maxima_times >= start_maxima_time, maxima_times <= end_maxima_time
                )
            )[0]
            if len(maxima_in_range) > 0:
                if len(maxima_in_range) == 1:
                    refined_maximas.append(np.array(local_maximas)[
                                           maxima_in_range][0])
                else:
                    maximas = np.array(local_maximas)[maxima_in_range]
                    true_maxima = maximas[np.argmax(maximas[:, 1])]
                    refined_maximas.append(true_maxima)

        if local_minimas[-1][0] < data_range[-1]:
            start_maxima_time = local_minimas[-1][0]
            maxima_times = np.array(local_maximas)[:, 0]
            maxima_in_range = np.where(
                np.logical_and(
                    maxima_times >= start_maxima_time, maxima_times <= data_range[-1]
                )
            )[0]
            if len(maxima_in_range) > 0:
                if len(maxima_in_range) == 1:
                    refined_maximas.append(np.array(local_maximas)[
                                           maxima_in_range][0])
                else:
                    maximas = np.array(local_maximas)[maxima_in_range]
                    true_maxima = maximas[np.argmax(maximas[:, 1])]
                    refined_maximas.append(true_maxima)

        return refined_maximas, refined_minimas

    def get_fitted_data(self, right_datas, left_datas):
        results = []
        for i in range(len(right_datas)):
            right_data = right_datas[i]
            left_data = left_datas[i]
            if i < 2:
                right_data_range, right_raw_data = self.get_eye_x_data(
                    right_data, "right_eye_locations"
                )
                left_data_range, left_raw_data = self.get_eye_x_data(
                    left_data, "left_eye_locations"
                )
                (
                    right_filtered_data,
                    right_local_maximas,
                    right_local_minimas,
                    right_qp_direction,
                    right_qp_velocity,
                    right_sp_velocity,
                    right_qp_time,
                    right_sp_time,
                ) = self.fit_data(right_data_range, right_raw_data)
                (
                    left_filtered_data,
                    left_local_maximas,
                    left_local_minimas,
                    left_qp_direction,
                    left_qp_velocity,
                    left_sp_velocity,
                    left_qp_time,
                    left_sp_time,
                ) = self.fit_data(left_data_range, left_raw_data)
            else:
                right_data_range, right_raw_data = self.get_eye_y_data(
                    right_data, "right_eye_locations"
                )
                left_data_range, left_raw_data = self.get_eye_y_data(
                    left_data, "left_eye_locations"
                )
                (
                    right_filtered_data,
                    right_local_maximas,
                    right_local_minimas,
                    right_qp_direction,
                    right_qp_velocity,
                    right_sp_velocity,
                    right_qp_time,
                    right_sp_time,
                ) = self.fit_data(right_data_range, right_raw_data)
                (
                    left_filtered_data,
                    left_local_maximas,
                    left_local_minimas,
                    left_qp_direction,
                    left_qp_velocity,
                    left_sp_velocity,
                    left_qp_time,
                    left_sp_time,
                ) = self.fit_data(left_data_range, left_raw_data)

            results.append(
                {
                    "right_filtered_data_range": right_data_range,
                    "right_filtered_data": right_filtered_data,
                    "left_filtered_data_range": left_data_range,
                    "left_filtered_data": left_filtered_data,
                    "right_local_maximas": right_local_maximas,
                    "right_local_minimas": right_local_minimas,
                    "left_local_maximas": left_local_maximas,
                    "left_local_minimas": left_local_minimas,
                }
            )
        return results

    def fit_data(self, data_range, raw_data):
        filtered_data = savgol_filter(
            raw_data,
            self.params["fit_window_size"],
            self.params["fit_polynomial_order"],
        )
        fit_maximas, fit_minimas = self.get_initial_extremas(
            data_range, filtered_data)

        (
            local_maximas,
            local_minimas,
            qp_direction,
            qp_speed,
            sp_speed,
            qp_time,
            sp_time,
        ) = self.refine_local_extremas(
            data_range,
            raw_data,
            fit_maximas,
            fit_minimas,
        )

        return (
            filtered_data,
            local_maximas,
            local_minimas,
            qp_direction,
            qp_speed,
            sp_speed,
            qp_time,
            sp_time,
        )

    def calculate_nystagmus_time(self, local_maximas, local_minimas):
        start_time = local_maximas[0][0]
        end_time = local_maximas[-1][0]
        if local_maximas[0][0] > local_minimas[0][0]:
            start_time = local_minimas[0][0]
        if local_maximas[-1][0] < local_minimas[-1][0]:
            end_time = local_minimas[-1][0]
        return end_time - start_time

    def compute_okn_stat(self, data, key, get_x=True):
        result = {"num_blinks": data["num_blinks"]}
        if get_x:
            data_range, raw_data = self.get_eye_x_data(data, key)
        else:
            data_range, raw_data = self.get_eye_y_data(data, key)

        (
            filtered_data,
            local_maximas,
            local_minimas,
            qp_direction,
            qp_velocity,
            sp_velocity,
            qp_time,
            sp_time,
        ) = self.fit_data(data_range, raw_data)

        result["time_nystagmus"] = self.calculate_nystagmus_time(
            local_maximas, local_minimas
        ) / np.max(data_range)

        if qp_direction == "Negative":
            result["nystagmus_latency"] = local_minimas[0][0]
            result["num_nystagmus"] = len(local_minimas) - 1
        else:
            result["nystagmus_latency"] = local_maximas[0][0]
            result["num_nystagmus"] = len(local_maximas) - 1

        result["qp_direction"] = qp_direction
        result["qp_velocity"] = qp_velocity
        result["sp_velocity"] = sp_velocity
        if get_x:
            result["sp_velocity_gain"] = self.params["x_target_time"] / sp_time
        else:
            result["sp_velocity_gain"] = self.params["y_target_time"] / sp_time
        return result

    def get_okn_stat(self, right_datas, left_datas):
        results = []
        for i in range(len(right_datas)):
            right_data = right_datas[i]
            left_data = left_datas[i]
            if i < 2:
                results.append(
                    {
                        "video": "okn_" + str(i),
                        "right": self.compute_okn_stat(
                            right_data, "right_eye_locations"
                        ),
                        "left": self.compute_okn_stat(left_data, "left_eye_locations"),
                    }
                )
            else:
                results.append(
                    {
                        "video": "okn_" + str(i),
                        "right": self.compute_okn_stat(
                            right_data,
                            "right_eye_locations",
                            get_x=False,
                        ),
                        "left": self.compute_okn_stat(
                            left_data,
                            "left_eye_locations",
                            get_x=False,
                        ),
                    }
                )
        return results

    def process(self):
        if isinstance(self.current_content, OknInstructionVideoScreen):
            if self.video_started:
                frame_data = oakd_capture({"reset_video": False,
                                           "video_file_name": self.current_content.record_name},
                                          service_states={"action": "capture",
                                                          "save_path": self.temp_collected_data_path})
                task_data = None
                if frame_data is not None:
                    iris_detection_result = iris_detection(
                        {"camera_input": frame_data})
                    if iris_detection_result is not None and not isinstance(iris_detection_result, ExceptionTypes):
                        task_data = iris_detection_result.get_data()

                if task_data:
                    if "right_iris_location" in task_data:
                        if len(task_data["right_iris_location"]) > 0:
                            self.right_eye_locations.append(
                                {
                                    "x": task_data["right_iris_location"][0]
                                    * self.camera_params["frame_width"],
                                    "y": task_data["right_iris_location"][1]
                                    * self.camera_params["frame_height"],
                                }
                            )
                    if "left_iris_location" in task_data:
                        if len(task_data["left_iris_location"]) > 0:
                            self.left_eye_locations.append(
                                {
                                    "x": task_data["left_iris_location"][0]
                                    * self.camera_params["frame_width"],
                                    "y": task_data["left_iris_location"][1]
                                    * self.camera_params["frame_height"],
                                }
                            )
                    if "num_blinks" in task_data:
                        self.num_blinks = task_data["num_blinks"]
