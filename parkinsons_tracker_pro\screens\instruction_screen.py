from cortic_platform.sdk.ui.basic_widgets import Container, Label
from cortic_platform.sdk.ui.input_widgets import Button
from cortic_platform.sdk.ui.viewer_widgets import NetworkVideoPlayer
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from common_utils import read_asset_image
from task_background_screen import TaskBackgroundContainer
from common_screen_element import TaskTitle

class InstructionScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1136, 850],
        instruction_text="Instruction Text",
        instruction_video_url="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
        radius=0,
        border_color=None,
        on_continue=None,
    ):
        super().__init__([0, 0, 1920, 1080])

        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name="Hand Movement",
                                                           task_icon=read_asset_image("hand.png")))
        
        self.content = Container([396, 150, 1136, 850])
        self.content.border_color = border_color
        self.content.corner_radius = radius
        self.content.alpha = 0

        self.instruction_text = Label(
            [0, 0, 1135, 88], data=instruction_text
        )
        self.instruction_text.font_size = 36

        self.instruction_video = NetworkVideoPlayer(
            [0, 108, 1136, 642], instruction_video_url
        )

        self.continue_button = Button(
            [497.67, 770, 140.67, 64]
        )
        self.continue_button.label = "Continue"
        self.continue_button.on_widget_event = on_continue

        self.alias = "Instruction"

        self.content.add_children([self.instruction_text, self.instruction_video, self.continue_button])

        self.task_background.update_content(self.content)
        self.add_child(self.task_background)
