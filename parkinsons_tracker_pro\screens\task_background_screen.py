from cortic_platform.sdk.ui.basic_widgets import Container, Image, Label, Icon
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from common_utils import read_asset_image


class MenuButton(Container):
    def __init__(self, rect=[0, 0, 224, 100], radius=0, border_color=None, on_event=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.capture_mouse_event = True
        self.on_widget_event = on_event
        self.background_color = "#5E7285"

        self.icon = Icon([40, 32, 32, 32], data="arrow-left")
        self.icon.icon_color = "#ffffff"

        self.label = Label(
            [82, 32, 105, 35], data="Main Menu"
        )
        self.label.font_size = 20
        self.label.font_color = "#ffffff"

        self.add_children([self.icon, self.label])


class TimeStampContainer(Container):
    def __init__(self, rect=[0, 0, 290, 64], patient_name="Patient Name", time_string="Time", radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color

        self.patient_name = Label(
            [0, 0, 290, 32], data=patient_name
        )
        self.patient_name.font_size = 24
        self.patient_name.font_weight = "bold"

        self.timestamp = Label(
            [0, 32, 290, 32], data=time_string)
        self.timestamp.font_size = 24

        self.add_children([self.patient_name, self.timestamp])


class HeaderContainer(Container):
    def __init__(
        self, rect=[0, 0, 1920, 100], radius=0, border_color=None, on_back_to_menu=None, time_string="", header_title=Container([0, 0, 1, 1])
    ):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.patient_name = ""
        self.patient_id = 0
        self.alpha = 1
        self.time_string = time_string

        self.menu_button = MenuButton(
            [0, 0, 224, 100], on_event=on_back_to_menu)
        
        self.title = header_title
        
        self.timestamp_label = Container([0, 0, 1, 1])
        self.timestamp_label.visible = False

        self.logo_image = Image(
            [1675.76, 20.94, 224.24, 58.13],
            data=read_asset_image("McKeown_logo_horizontal.png"),
        )

        self.add_children([self.menu_button, self.title, self.timestamp_label, self.logo_image])

    def update_title(self, new_title):
        for i in range(len(self.children)):
            if self.children[i]._id == self.title._id:
                self.children[i] = new_title
                self.title = new_title
                break
        self.root_widget_tree.update(self)

    def show_timestamp(self):
        new_widget = TimeStampContainer(
            [264, 18, 290, 64], self.patient_name, self.time_string
        )
        
        for i in range(len(self.children)):
            if self.children[i]._id == self.timestamp_label._id:
                self.children[i] = new_widget
                self.timestamp_label = new_widget
                break
        self.root_widget_tree.update(self)

        self.timestamp_label.visible = True

        for i in range(len(self.children)):
            if self.children[i]._id == self.timestamp_label._id:
                self.children[i].visible = True
                break
        self.root_widget_tree.update(self.timestamp_label)

    def hide_timestamp(self):
        self.timestamp_label.visible = False
        for i in range(len(self.children)):
            if self.children[i]._id == self.timestamp_label._id:
                self.children[i].visible = False
                break
        self.root_widget_tree.update(self.timestamp_label)


class ContentContainer(Container):
    def __init__(self, rect=[0, 0, 1920, 1080], radius=0, border_color=None, on_back_to_menu=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.content = Container([0, 0, 1, 1])
        self.children.append(self.content)

    def update_content(self, new_content):
        for i in range(len(self.children)):
            if self.children[i]._id == self.content._id:
                self.children[i] = new_content
                self.content = new_content
                break
        self.root_widget_tree.update(self)


class FooterContainer(Container):
    def __init__(self, rect=[0, 0, 1920, 62], radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1

        self.copyright_label = Label(
            [20, 20, 300, 22], data="Copyright \\u00a9 2023, The McKeown Lab"
        )
        
        self.cortic_label = Label([1665.97, 20, 73, 22], data="Powerd by")

        self.cortic_logo = Image(
            [1746.97, 20.81, 153.03, 20.39],
            data=read_asset_image("cortic_horizontal.png"),
        )

        self.add_children([self.copyright_label, self.cortic_label, self.cortic_logo])


class FinishCalibrationContainer(Container):
    def __init__(self, rect=[0, 0, 1920, 1080], radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 0.25
        self.background_color = "#000000"

        self.ok_container = Container([748, 328, 425, 425])
        self.ok_container.alpha = 1
        self.ok_container.corner_radius = 30

        self.ok_img = Image(
            [100, 100, 225, 225],
            data=read_asset_image("ok_big.png"),
        )

        self.ok_container.add_child(self.ok_img)
        self.add_child(self.ok_container)


class TaskBackgroundContainer(Container):
    def __init__(
        self, rect=[0, 0, 1920, 1080], radius=0, border_color=None, on_back_to_menu=None, time_string="", header_title=Container([0, 0, 1, 1])
    ):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.background_color = "#E6EDF7"
        self.time_string = time_string

        self.header_container = HeaderContainer(
            [0, 0, 1920, 100],
            on_back_to_menu=on_back_to_menu,
            time_string=self.time_string,
            header_title=header_title
        )

        self.content_container = Container([1920, 0, 1, 1])

        self.buffer_content_container = Container([1920, 0, 1, 1])
        self.buffer_content_container.opacity = 0

        self.footer_container = FooterContainer([0, 1018, 1920, 62])

        self.finish_calibration_popup = FinishCalibrationContainer(
            [0, 0, 1920, 1080])
        self.finish_calibration_popup.visible = False

        self.add_children([self.header_container, self.content_container, self.buffer_content_container, self.footer_container, self.finish_calibration_popup])

        # self.visible = False

    def show_calibration_finish(self):
        self.finish_calibration_popup.visible = True
        for i in range(len(self.children)):
            if self.children[i]._id == self.finish_calibration_popup._id:
                self.children[i].visible = True
                break
        self.root_widget_tree.update(
            self.finish_calibration_popup)

    def hide_calibration_finish(self):
        self.finish_calibration_popup.visible = False
        for i in range(len(self.children)):
            if self.children[i]._id == self.finish_calibration_popup._id:
                self.children[i].visible = False
                break
        self.root_widget_tree.update(
            self.finish_calibration_popup)

    def update_content(self, new_content):
        for i in range(len(self.children)):
            if self.children[i]._id == self.content_container._id:
                self.children[i] = new_content
                self.content_container = new_content
                break
        # self.root_widget_tree.update(
        #     self
        # )

    def show_footer(self):
        self.footer_container.visible = True
        self.root_widget_tree.update(self.footer_container)

    def hide_footer(self):
        self.footer_container.visible = False
        self.root_widget_tree.update(self.footer_container)

    def reset(self):
        self.visible = False
        self.update_content(Container([1920, 0, 1, 1]))
        self.header_container.update_title(Container([0, 0, 1, 1]))
        self.header_container.hide_timestamp()
