from base_module import BaseModule
from tasks.eye_movement.okn_task import OknTask
from tasks.eye_movement.calibration_task import CalibrationTask
from utils import read_asset_image
from task_data import instruction_videos
from service_registry import *
import numpy as np


class EyeTrackingModule(BaseModule):
    def __init__(self, background, on_module_complete=None):
        super().__init__(
            "Eye Movement",
            read_asset_image("eye.png"),
            background,
            on_module_complete,
        )
        self.task_list = [CalibrationTask, OknTask]

    def init_resources(self):
        self.session.loading_background.visible = True
        self.session.widget_tree.update(self.session.loading_background)
        self.request_module_resouces(
            [
                instruction_videos["okn"][0]["filename"],
                instruction_videos["okn"][1]["filename"],
                instruction_videos["okn"][2]["filename"],
                instruction_videos["okn"][3]["filename"],
            ]
        )
        self.session.app.reset_service_state("oakd_capture")
        self.session.app.reset_service_state("head_pose_estimation")
        self.session.app.reset_service_state("iris_detection")
        # Calling the services to activate them under task mode.
        oakd_capture({"reset_video": True,
                      "video_file_name": ""}).get_data()
        head_pose_estimation(
            {"camera_input": {"frame": np.zeros((720, 1280, 3), np.uint8),
                              "camera_matrix": np.zeros((3, 3), np.float32),
                              "frame_width": 1280,
                              "frame_height": 720}}).get_data()
        iris_detection({"camera_input": {"frame": np.zeros((720, 1280, 3), np.uint8),
                                         "camera_matrix": np.zeros((3, 3), np.float32),
                                         "frame_width": 1280,
                                         "frame_height": 720}}).get_data()
        self.session.loading_background.visible = False
        self.session.widget_tree.update(self.session.loading_background)

    def on_task_complete(self, data=None, task_result=None):
        super().on_task_complete()
        if data == "calibration":
            self.session.loading_background.visible = True
            self.session.widget_tree.update(self.session.loading_background)
            self.request_module_resouces(
                [
                    instruction_videos["okn"][0]["filename"],
                    instruction_videos["okn"][1]["filename"],
                    instruction_videos["okn"][2]["filename"],
                    instruction_videos["okn"][3]["filename"],
                ]
            )
            self.session.loading_background.visible = False
            self.session.widget_tree.update(self.session.loading_background)
            self.next_task(task_result)
        else:
            self.on_module_complete()
