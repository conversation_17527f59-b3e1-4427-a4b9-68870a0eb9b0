{"service_name": "Face Landmarks", "developer_identifier": "78ba05ff-803b-4656-a32a-99b56be0c597", "description": "This service detects landmarks of a human face in an image provided as an OpenCV Mat object. It uses Google MediaPipe's Face Landmarker solution", "major_version": "0", "minor_version": "1", "architecture": "x86_64, arm64, aarch64", "hardware_requirements": {"min_num_cpu_core": 1, "min_cpu_frequency": 0, "min_free_memory": 0, "min_free_disk": 0, "required_connected_usb_devices": []}, "is_data_source": false, "service_class": "FaceLandmarks", "processing_queue_size": 100, "service_processing_fps": 30, "service_id": "be00829d-51ab-4e11-a68b-d483b4c325ca"}