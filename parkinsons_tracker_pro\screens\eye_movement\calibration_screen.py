from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image, VerticalSeparator, HorizontalSeparator
from cortic_platform.sdk.ui.misc_widgets import CircularLoader, CircularCounter
from utils import read_asset_image
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)) + "/../..")
from config import Config
import math
from task_data import messages

class AnimatedIrisTarget(Container):
    def __init__(self, rect=[0, 0, 150, 150], calibration_time=5, radius=0, border_color="#E6EDF7"):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.opacity = 0
        self.background_color = "#E6EDF7"
        self.alpha = 0
        self.visibility_animation_duration = 50
        self.calibration_time = calibration_time

        self.time_meter = CircularCounter(
            [0, 0, rect[2], rect[3]]
        )
        self.time_meter.counter_font_size = 36
        self.time_meter.ring_thickness = 11
        self.time_meter.end_value = calibration_time
        self.time_meter.show_counter_value = False
        self.time_meter.ring_color = "#ffffff"

        self.margin = 25

        self.animated_image = Image(
            [
                self.margin,
                self.margin,
                rect[2] - 2 * self.margin,
                rect[3] - 2 * self.margin,
            ],
        )
        self.animated_image.corner_radius = (rect[2] - 2 * self.margin) / 2

        self.add_children(
            [self.time_meter, self.animated_image]
        )


class NumberIrisTarget(Container):
    def __init__(
        self, rect=[0, 0, 150, 150], number=1, calibration_time=5, radius=0, border_color="#E6EDF7"
    ):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.opacity = 0
        self.background_color = "#E6EDF7"
        self.alpha = 0
        self.calibration_time = calibration_time

        self.time_meter = CircularCounter(
            [0, 0, rect[2], rect[3]]
        )
        self.time_meter.counter_font_size = 36
        self.time_meter.ring_thickness = 11
        self.time_meter.end_value = calibration_time
        self.time_meter.show_counter_value = False
        self.time_meter.ring_color = "#ffffff"

        self.margin = 25

        self.number_label = Label(
            [
                self.margin,
                self.margin,
                rect[2] - 2 * self.margin,
                rect[3] - 2 * self.margin,
            ],
            data=number,
        )
        self.number_label.background_color = "#E6EDF7"
        self.number_label.alpha = 0
        self.number_label.font_size = 60
        self.number_label.font_weight = "bold"
        self.number_label.alignment = "center"

        self.add_children(
            [self.time_meter, self.number_label]
        )

class OKCounter(Container):
    def __init__(self, rect=[0, 0, 200, 200], calibration_time=2, radius=0, border_color="#E6EDF7"):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.background_color = "#E6EDF7"
        self.alpha = 0

        self.calibration_time = calibration_time

        self.time_meter = CircularCounter(
            [0, 0, rect[2], rect[3]]
        )
        self.time_meter.radius = 20
        self.time_meter.counter_font_size = 36
        self.time_meter.ring_thickness = 4
        self.time_meter.end_value = calibration_time
        self.time_meter.show_counter_value = False
        self.time_meter.ring_color = "#ffffff"

        self.add_child(self.time_meter)


class HeadFrontalContainer(Container):
    def __init__(self, rect=[0, 0, 500, 35], text_length=500, radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 0

        self.ok_label = Label(
            [0, 0, text_length, 30],
            data="OK"
        )
        self.ok_label.font_size = 23
        self.ok_label.font_color = "#000000"
        self.ok_label.alignment = "center"

        self.alert_label = Label(
            [0, 0, text_length, 30],
            data=""
        )
        self.alert_label.font_size = 23
        self.alert_label.font_color = "#ff0000"
        self.alert_label.alignment = "center"
        self.alert_label.visible = False
        
        self.add_children(
            [self.ok_label, self.alert_label]
        )

    def show_alert(self, alert_text):
        self.alert_label.set_data(alert_text)
        self.root_widget_tree.update()
        self.ok_label.visible = False
        self.root_widget_tree.update(self.ok_label)
        self.alert_label.visible = True
        self.root_widget_tree.update(self.alert_label)
        self.alert_label.set_data(alert_text)

    def show_ok(self):
        self.alert_label.visible = False
        self.root_widget_tree.update(self.alert_label)
        self.ok_label.visible = True
        self.root_widget_tree.update(self.ok_label)


class CalibrationScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1920, 1080],
        region_width=500,
        region_height=500,
        iris_target_size=150,
        circular_region_diameter=200,
        head_pose_calibration_time=2,
        iris_calibration_time=3,
        radius=0,
        border_color=None,
    ):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.background_color = "#E6EDF7"

        self.region_width = region_width
        self.line_step = region_width / 12
        self.region_height = self.region_width - 1 * self.line_step
        self.circular_region_diameter = circular_region_diameter
        self.head_pose_calibration_time = head_pose_calibration_time

        self.center_region = Container(
            [
                1920 / 2 - self.region_width / 2 + 2,
                1080 / 2 - self.region_height / 2 + 1,
                self.region_width - 3,
                self.region_height - 3,
            ]
        )
        self.center_region.border_thickness = 1
        self.center_region.border_color = "#E6EDF7"
        self.center_region.background_color = "#E6EDF7"

        self.target_image = read_asset_image("head_target.png")

        self.x1 = region_width / 2
        self.y1 = self.region_height / 2
        self.r1 = circular_region_diameter / 2
        self.r2 = circular_region_diameter / 9
        self.iris_target_size = iris_target_size

        self.instruction_text = Label(
            [15, 5, region_width - 30, 80],
            data=messages["move_head_hint"],
        )
        self.instruction_text.font_size = 21.5
        self.instruction_text.font_weight = "bold"
        self.instruction_text.alignment = "center"

        self.head_pose_image = Image(
            [50, 90 + 2 * self.line_step, 400, 225]
        )

        self.calibration_loading_container = Container(
            [50, 90 + 2 * self.line_step, 400, 225]
        )
        self.calibration_loading_container.alpha = 0.4
        self.calibration_loading_container.background_color = "#000000"
        self.calibration_loading_container.visible = False

        self.loading_animation = CircularLoader([135, 47.5, 130, 130])

        self.calibration_loading_container.add_child(
            self.loading_animation)
        
        self.ok_counter = OKCounter(
            [150, 102.5 + 2 * self.line_step, 200, 200])
        self.ok_counter.visible = False

        self.target_ring = CircularCounter(
            [
                self.x1 - circular_region_diameter / 6 - 10,
                85 + self.line_step - circular_region_diameter / 6 - 10,
                circular_region_diameter / 6 * 2 + 20,
                circular_region_diameter / 6 * 2 + 20,
            ]
        )
        self.target_ring.radius = 40
        self.target_ring.counter_font_size = 12
        self.target_ring.ring_thickness = 8
        self.target_ring.end_value = head_pose_calibration_time
        self.target_ring.show_counter_value = False
        self.target_ring.ring_color = "#C0C0C0"

        self.target_image = Image(
            [
                self.x1 - self.r2,
                85 + self.line_step - self.r2,
                self.r2 * 2,
                self.r2 * 2,
            ],
            data=self.target_image,
        )

        self.iris_targets = []
        self.animated_gifs = []
        self.gif_init_count = 4
        for i in range(10):
            self.animated_gifs.append(
                read_asset_image("indicator_" + str(i + 1) + ".gif")
            )
        for i in range(4):
            target = NumberIrisTarget(
                [0, 0, iris_target_size, iris_target_size],
                i + 1,
                calibration_time=iris_calibration_time,
            )
            target.alpha = 0.3
            self.iris_targets.append(target)
        for j in range(10):
            target = AnimatedIrisTarget(
                [0, 0, iris_target_size, iris_target_size],
                calibration_time=iris_calibration_time,
            )
            self.iris_targets.append(target)

        self.current_iris_target = None

        self.head_frontal_container = HeadFrontalContainer(
            [0, 325 + 2 * self.line_step, self.region_width, 35], self.region_width
        )

        self.calibration_alert_container = Container(
            [50, 90 + 2 * self.line_step, 400, 225]
        )
        self.calibration_alert_container.alpha = 0.4
        self.calibration_alert_container.background_color = "#000000"
        self.calibration_alert_container.visible = False

        self.alert_image = Image(
            [135, 57.5, 130, 110], data=read_asset_image("alert.png")
        )
        
        self.calibration_alert_container.add_child(self.alert_image)

        self.center_region.add_children(
            [self.instruction_text, 
             self.head_pose_image,
             self.calibration_loading_container,
             self.calibration_alert_container,
             self.head_frontal_container,
             self.ok_counter,
             self.target_ring,
             self.target_image,
            ]
        )

        left_width = int(Config.monitor_width_px / 2 - region_width / 2)
        num_lines = math.ceil(left_width / self.line_step)
        for i in range(0, num_lines):
            vertical_separator = VerticalSeparator(
                [left_width - i * self.line_step, 0, 1, Config.monitor_height_px]
            )
            vertical_separator.line_color = "#808080"
            vertical_separator.thickness = 1
            self.add_child(vertical_separator)

        num_lines = math.ceil(
            (Config.monitor_width_px - left_width) / self.line_step)
        for i in range(1, num_lines):
            vertical_separator = VerticalSeparator(
                [left_width + i * self.line_step, 0, 1, Config.monitor_height_px]
            )
            vertical_separator.line_color = "#808080"
            vertical_separator.thickness = 1
            self.add_child(vertical_separator)

        top_height = int(Config.monitor_height_px / 2 - self.region_height / 2)
        num_lines = math.ceil(top_height / self.line_step)
        for i in range(0, num_lines):
            vertical_separator = HorizontalSeparator(
                [0, top_height - i * self.line_step, Config.monitor_width_px, 1]
            )
            vertical_separator.line_color = "#808080"
            vertical_separator.thickness = 1
            self.add_child(vertical_separator)

        num_lines = math.ceil((Config.monitor_height_px - top_height) / 2)
        for i in range(1, num_lines):
            vertical_separator = HorizontalSeparator(
                [0, top_height + i * self.line_step, Config.monitor_width_px, 1]
            )
            vertical_separator.line_color = "#808080"
            vertical_separator.thickness = 1
            self.add_child(vertical_separator)
    
        self.add_child(self.center_region)
        for i in range(len(self.iris_targets)):
            self.add_child(self.iris_targets[i])

    def show_calibration_loader(self):
        self.calibration_loading_container.visible = True
        self.root_widget_tree.update(
            self.calibration_loading_container
        )

    def show_alert(self):
        self.calibration_alert_container.visible = True
        self.root_widget_tree.update(self.calibration_alert_container)

    def hide_alert(self):
        self.calibration_alert_container.visible = False
        self.root_widget_tree.update(self.calibration_alert_container)

    def show_ok_container(self):
        self.ok_counter.visible = True
        self.root_widget_tree.update(self.ok_counter)

    def hide_ok_container(self):
        self.ok_counter.visible = False
        self.root_widget_tree.update(self.ok_counter)

    def update_target_location(self, x_offset, y_offset):
        self.target_image.rect = [x_offset, y_offset, self.r2 * 2, self.r2 * 2]
        self.root_widget_tree.update(self.target_image)

    def initalized_iris_target_gifs(self):
        for i in range(len(self.animated_gifs)):
            self.iris_targets[i +
                              4].animated_image.set_data(self.animated_gifs[i])

    def switch_to_iris_calibration(self):
        self.calibration_loading_container.visible = False
        self.root_widget_tree.update(
            self.calibration_loading_container
        )
        self.instruction_text.set_data(messages["iris_target_hint"])

    def show_iris_target(self, iris_targets, index):
        x = iris_targets[index][0]
        y = iris_targets[index][1]
        if self.current_iris_target is not None:
            self.current_iris_target.opacity = 0
            self.root_widget_tree.update(
                self.current_iris_target, use_existing_data=True
            )
        self.current_iris_target = self.iris_targets[index]
        self.current_iris_target.rect = [
            x,
            y,
            self.iris_target_size,
            self.iris_target_size,
        ]
        self.current_iris_target.time_meter.set_data(0)
        self.root_widget_tree.update()
        self.current_iris_target.opacity = 1
        self.root_widget_tree.update(
            self.current_iris_target, use_existing_data=True
        )
