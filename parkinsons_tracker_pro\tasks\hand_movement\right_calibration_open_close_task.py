from base_task import BaseTask
from screens.hand_movement.right_hand_calibration_screen import (
    RightHandCalibrationScreen,
)
from screens.video_instruction_screen import VideoInstructionScreen
from screens.common_screen_element import TaskTitle
from task_data import instructions
from task_data import messages
import time
from service_registry import *
from cortic_platform.sdk.app_events import ExceptionTypes


class RightCalibrationOpenCloseTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__(
            "right_calibration_open_close", module, on_task_complete, task_params
        )
        self.current_hand = "right"
        self.title = TaskTitle(
            [840, 32, 571, 42], self.module.name, self.module.icon)
        self.face_visible = ""
        self.hand_visible = ""
        self.screens = [
            VideoInstructionScreen(
                [200, 90, 1520, 950],
                instructions["hand_calibration_open_close"],
                self.module.resources["Hand Open and Close.mp4"],
                subtitle="Open and Close Hand",
                font_size=28,
                text_height=120,
                on_continue=self.next_screen,
            ),
            RightHandCalibrationScreen([0, 100, 1920, 918]),
        ]
        self.countdown_time = 1
        self.calibrated = False
        self.showed_finish_symbol = False
        self.calibration_ok_time = time.time()
        self.hold_on_time = 1
        self.calibration_finish_time = time.time()
        self.show_finishing_symbol_time = 1

        # delete later
        self.start_time = time.time()
        self.counter = 0
        self.times = []
        self.times2 = []
        self.times3 = []

    def next_screen(self, data=None):
        if isinstance(self.current_content, VideoInstructionScreen):
            self.title = TaskTitle(
                [840, 32, 571, 42], self.module.name, self.module.icon
            )
            self.current_content = self.screens[1]
            self.current_content.task_background.header_container.menu_button.on_widget_event = self.module.session.on_back_to_menu
            self.module.session.update_current_task_screen(self.current_content)
        else:
            self.on_task_complete("right_calibration_open_close")

    def process(self):
        if self.is_running:
            if isinstance(self.current_content, RightHandCalibrationScreen):
                # self.counter += 1
                # t1 = time.time()
                frame_data = oakd_capture({"reset_video": False, "video_file_name": ""},
                                          service_states={"action": "capture"})
                # self.times.append(time.time() - t1)
                task_data = None
                if frame_data is not None:
                    # t1 = time.time()
                    finger_tapping_data = finger_tapping_detector(
                        {"camera_input": frame_data})
                    # self.times2.append(time.time() - t1)
                    if finger_tapping_data is not None and not isinstance(finger_tapping_data, ExceptionTypes):
                        # t1 = time.time()
                        task_data = finger_tapping_data.get_data()
                        # self.times3.append(time.time() - t1)

                if task_data:
                    if isinstance(task_data, ExceptionTypes):
                        print("Exception: ", task_data)
                        return
                    if "frame" in task_data:
                        self.current_content.main_camera_image.set_data(
                            task_data["frame"])
                        hand_visible = self.is_hand_visible(
                            task_data["hand_infos"], self.current_hand, 0.05
                        )
                        self.current_content.right_hand_visibility_container.hint_label.data = (
                            hand_visible
                        )
                        if hand_visible == "OK":
                            if self.hand_visible != "OK":
                                self.current_content.right_hand_visibility_container.is_visible()
                        else:
                            if self.hand_visible == "OK":
                                self.current_content.right_hand_visibility_container.not_visible()
                        self.hand_visible = hand_visible
                        if hand_visible != "OK":
                            self.calibrated = False
                        if hand_visible == "OK":
                            if not self.calibrated:
                                self.calibrated = True
                                self.calibration_ok_time = time.time()
                            else:
                                if not self.showed_finish_symbol:
                                    if (
                                        time.time() - self.calibration_ok_time
                                    ) >= 1:
                                        self.current_content.task_background.show_calibration_finish()
                                        self.showed_finish_symbol = True
                                        self.calibration_finish_time = time.time()
                                else:
                                    if (
                                        time.time() - self.calibration_finish_time
                                    ) >= self.show_finishing_symbol_time:
                                        self.current_content.task_background.hide_calibration_finish()
                                        self.on_task_complete(
                                            "right_calibration_open_close"
                                        )

                # if (time.time() - self.start_time) >= 1:
                #     print("FPS: ", self.counter)
                #     print("Average task1 waiting time: ",
                #           sum(self.times) / len(self.times))
                #     print("Average task2 waiting time: ",
                #           sum(self.times2) / len(self.times2))
                #     print("Average task3 waiting time: ",
                #           sum(self.times3) / len(self.times3))
                #     self.counter = 0
                #     self.start_time = time.time()
                #     self.times = []
                #     self.times2 = []
                #     self.times3 = []

    def is_face_visible(self, face, bound_percentage):
        if len(face) == 0:
            return messages["face_not_visible"]
        if (
            face[0]["face_coordinates"][0] > bound_percentage
            and face[0]["face_coordinates"][1] > bound_percentage
            and face[0]["face_coordinates"][2] < (1 - bound_percentage)
            and face[0]["face_coordinates"][3] < (1 - bound_percentage)
        ):
            return messages["ok"]
        else:
            if face[0]["face_coordinates"][0] <= bound_percentage:
                return messages["move_left"]
            if face[0]["face_coordinates"][1] <= bound_percentage:
                return messages["move_lower"]
            if face[0]["face_coordinates"][2] >= (1 - bound_percentage):
                return messages["move_right"]
            if face[0]["face_coordinates"][3] >= (1 - bound_percentage):
                return messages["move_upper"]

    def is_hand_visible(self, hand_infos, handness, bound_percentage):
        if len(hand_infos) == 0:
            return messages["hand_not_visible"]
        hand_info = None
        for hand in hand_infos:
            if hand["label"] == handness:
                hand_info = hand
        if hand_info is None:
            return messages["wrong_hand"]
        within_bounds = True
        for location in hand_info["landmarks"]:
            if (
                location[0] / 1280.0 < bound_percentage
                or location[0] / 1280.0 >= (1 - bound_percentage)
                or location[1] / 720.0 < bound_percentage
                or location[1] / 720.0 >= (1 - bound_percentage)
            ):
                within_bounds = False
        if within_bounds:
            return messages["ok"]
        else:
            return messages["not_fully_visible"]
