from base_task import BaseTask
from screens.common_screen_element import TaskTitle
from screens.hand_movement.still_collection_screen import StillCollectionScreen
from screens.module_complete_screen import ModuleCompleteScreen
import os
import json
import time
from service_registry import *
import sys
from pathlib import Path
from task_data import messages
from cortic_platform.sdk.app_events import ExceptionTypes

class StillCollectionTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("still_collection", module, on_task_complete, task_params)
        with open(
            os.path.dirname(os.path.realpath(__file__)) + "/parameters.json"
        ) as f:
            self.params = json.load(f)
        self.current_hand = task_params
        self.temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/temp_collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/still_collection/"
        )
        self.collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/still_collection/"
        )
        Path(self.temp_collected_data_path).mkdir(parents=True, exist_ok=True)
        self.module.session.logger.info(
            "Created temp data collection dir for " + "both" + " hand still task"
        )
        # os.makedirs(os.path.dirname(self.collected_data_path), exist_ok=True)
        self.title = TaskTitle(
            [840, 32, 571, 42], self.module.name, self.module.icon)
        self.screens = [
            StillCollectionScreen([0, 100, 1920, 918]),
            ModuleCompleteScreen(
                    [0, 100, 1920, 918],
                    instruction_text="Hand Movement Module Completed",
                    task_name=module.name,
                    asset_image=module.icon,
                    on_module_complete=self.on_module_complete,
                    on_redo_previous=self.on_redo_previous
                )
            ]
        self.flat_once = False
        self.num_seconds = 0
        self.start_time = None
        self.target_seconds = 10
        oakd_capture({"reset_video": True, "video_file_name": ""}).get_data()
        self.module.session.logger.info(
            "("
            + self.module.session.patient_id
            + ") "
            + self.name
            + " task initialized"
        )

    def on_redo_previous(self, data=None):
        self.on_task_complete("both_calibration_still", redo=True)

    def on_module_complete(self, data=None):
        self.on_task_complete(self.current_hand + "_still_collection")

    def get_middle_distance(self, hand_infos):
        right_distance = -1
        left_distance = -1
        for hand in hand_infos:
            if hand["label"] == "right":
                right_distance = hand["middle_y_distance"]
            else:
                left_distance = hand["middle_y_distance"]
        return right_distance, left_distance

    def process(self):
        if isinstance(self.current_content, StillCollectionScreen):
            if self.num_seconds >= self.target_seconds:
                oakd_capture({"reset_video": True, "video_file_name": ""})
                self.module.session.logger.info(
                    self.name + " task complete, checking if data is recorded..."
                )
                if not os.path.isfile(
                    self.temp_collected_data_path
                    + self.current_hand
                    + "_still"
                    + ".h264"
                ):
                    self.module.session.logger.error(
                        "Video File for task "
                        + self.name
                        + " is not found, raising exception"
                    )
                    self.module.session.logger.error(
                        self.name + " task recorded data not found"
                    )
                    sys.stderr.write(
                        json.dumps(
                            {
                                "app_exception_msg": self.name
                                + ": "
                                + messages["recordedDataNotFound"]
                            }
                        )
                        + "\n"
                    )
                    raise Exception(messages["recordedDataNotFound"])
                else:
                    self.module.session.logger.info(
                        self.name + " task recorderd data found..."
                    )
                self.current_screen_index += 1
                self.current_content = self.screens[self.current_screen_index]
                self.current_content.task_background.header_container.menu_button.on_widget_event = self.module.session.on_back_to_menu
                self.module.session.update_current_task_screen(self.current_content)
                # self.on_task_complete(self.current_hand + "_still_collection")
            else:
                frame_data = oakd_capture({"reset_video": False, "video_file_name": self.current_hand + "_still"},
                                          service_states={"action": "capture",
                                                          "save_path": self.temp_collected_data_path})
                task_data = None
                if frame_data is not None:
                    finger_tapping_data = finger_tapping_detector(
                        {"camera_input": frame_data})
                    if finger_tapping_data is not None and not isinstance(finger_tapping_data, ExceptionTypes):
                        task_data = finger_tapping_data.get_data()
                if task_data:
                    self.current_content.main_camera_image.data = task_data["frame"]
                    (
                        right_middle_distance,
                        left_middle_distance,
                    ) = self.get_middle_distance(task_data["hand_infos"])
                    if not self.flat_once:
                        if (
                            right_middle_distance != -1
                            and left_middle_distance != -1
                        ):
                            # if (
                            #     abs(right_middle_distance)
                            #     <= self.params["flat_threshold"]
                            #     and abs(left_middle_distance)
                            #     <= self.params["flat_threshold"]
                            # ):
                            self.flat_once = True
                            self.current_content.start_statistic()
                            self.start_time = time.time()
                    else:
                        elapsed_time = time.time() - self.start_time
                        if elapsed_time >= 1:
                            self.num_seconds += 1
                            self.start_time = time.time()
                            elapsed_time = 0
                        self.current_content.stat_container.time_meter.data = (
                            self.num_seconds + elapsed_time
                        )
