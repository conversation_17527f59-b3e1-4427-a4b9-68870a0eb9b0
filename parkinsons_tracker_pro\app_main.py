from cortic_platform.sdk import App
from cortic_platform.sdk.app_events import ExceptionTypes, AppActions
from cortic_platform.sdk.logging import log, LogLevel
from session import Session
import os
import sys
import logging
from datetime import datetime
import traceback

logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s | %(levelname)s | %(message)s")

stdout_handler = logging.StreamHandler(sys.stdout)
stdout_handler.setLevel(logging.DEBUG)
stdout_handler.setFormatter(formatter)

file_handler = logging.FileHandler(
    os.path.dirname(os.path.realpath(__file__))
    + "/logs/"
    + datetime.today().strftime("%Y-%m-%d")
    + "_logs.log"
)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(formatter)


logger.addHandler(file_handler)
logger.addHandler(stdout_handler)


class McKeownDataCollectionApp(App):
    def __init__(self):
        super().__init__()

    def setup(self):
        self.session = Session(self.widget_tree, self, logger)

    def process(self):
        try:
            if self.session.current_module is not None:
                if self.session.current_module.current_task is not None:
                    if self.session.current_module.current_task.is_running:
                        self.session.current_module.current_task.process()
            self.widget_tree.update()
        except Exception as e:
            log("Exception happened", log_level=LogLevel.Error)
            log(str(traceback.format_exc()), log_level=LogLevel.Error)
            logger.error(str(traceback.format_exc()))
            raise e

    def on_exception(self, exception, exception_data=None):
        log("Exception happened", log_level=LogLevel.Error)
        if exception.type == ExceptionTypes.NotSchedulable:
            return AppActions.Continue
        if exception.type == ExceptionTypes.NodeExpired:
            return AppActions.Continue

    def teardown(self):
        log("Teardown function called")
