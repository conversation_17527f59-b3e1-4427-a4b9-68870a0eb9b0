from cortic_platform.sdk.ui.basic_widgets import Container, Label
from cortic_platform.sdk.ui.input_widgets import Button
from cortic_platform.sdk.ui.viewer_widgets import NetworkVideoPlayer
from cortic_platform.sdk.ui.misc_widgets import CircularLoader
from task_data import messages


class OknInstructionLabelScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1520, 850],
        instruction_text="Instruction text",
        subtitle=None,
        text_width=1000,
        text_height=400,
        font_size=48,
        radius=0,
        border_color=None,
        on_continue=None,
    ):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 0

        self.instruction_text = Label(
            [260, 200, text_width, text_height],
            data=instruction_text
        )
        self.instruction_text.alignment = "center"
        self.instruction_text.font_size = font_size

        self.continue_button = Button(
            [690, 770, 140, 64]
        )
        self.continue_button.label = "Continue"
        self.continue_button.on_widget_event = on_continue

        if subtitle is not None:
            self.subtitle = Label([10, 10, 1135, 100], data=subtitle)
            self.subtitle.font_size = 84
            self.subtitle.alignment = "left"
            self.add_child(self.subtitle)

        self.calibration_loading_container = Container([0, 0, 1520, 850])
        self.calibration_loading_container.alpha = 0.4
        self.calibration_loading_container.background_color = "#000000"
        self.calibration_loading_container.visible = False

        self.loading_animation = CircularLoader([660, 325, 200, 200])

        self.calibration_loading_container.add_child(
            self.loading_animation)
        
        self.add_children(
            [self.instruction_text, self.continue_button, self.calibration_loading_container]
        )

    def enable_waiting_icon(self):
        self.calibration_loading_container.visible = True
        self.root_widget_tree.update(
            self.calibration_loading_container
        )

    def disable_waiting_icon(self):
        self.calibration_loading_container.visible = False
        self.root_widget_tree.update(
            self.calibration_loading_container
        )


class OknInstructionVideoScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1920, 1080],
        instruction_video_url="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
        radius=0,
        border_color=None,
        on_continue=None,
        on_video_event=None,
        on_redo=None,
    ):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color

        self.instruction_video = NetworkVideoPlayer(
            [0, 0, 1920, 1080], instruction_video_url
        )

        self.record_name = instruction_video_url.split("/")[-1].split(".")[0]
        self.on_video_event = on_video_event
        self.instruction_video.on_widget_event = self._on_video_event
        self.on_continue = on_continue
        self.on_redo = on_redo

        self.add_child(self.instruction_video)
        self.video_file_not_found_counter = 0

    def _on_video_event(self, data):
        event_handle_status = self.on_video_event(data)
        if data == "stopped":
            if (event_handle_status):
                print("video stopped, continue to next screen")
                self.on_continue()
            else:
                self.video_file_not_found_counter += 1
                if (self.video_file_not_found_counter > 2):
                    raise Exception(messages["recordedDataNotFound"])
                print("recorded video file not found, retry previous okn task, attempt %d" % (self.video_file_not_found_counter + 1))
                self.on_redo()
