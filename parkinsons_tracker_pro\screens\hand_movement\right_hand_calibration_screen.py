from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image
from cortic_platform.sdk.ui.input_widgets import Button
from utils import read_asset_image
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)) + "/../..")
from screens.task_background_screen import TaskBackgroundContainer
from screens.common_screen_element import TaskTitle
from task_data import messages


class HandVisibilityContainer(Container):
    def __init__(self, rect=[0, 0, 495, 104], hand="Right", radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 0

        self.not_visible_icon = Image(
            [20, 0, 56, 56], data=read_asset_image("warning.png")
        )

        self.visible_icon = Image(
            [20, 0, 56, 56], data=read_asset_image("ok.png"))
        self.visible_icon.visible = False

        self.title_label = Label(
            [0, 4, 495, 53],
            data=hand + messages["hand_visible"],
        )
        self.title_label.font_size = 38
        self.title_label.font_weight = "bold"
        self.title_label.alignment = "center"

        self.hint_label = Label(
            [0, 56, 495, 48],
            data=messages["hand_needs_visible"],
        )
        self.hint_label.font_size = 34
        self.hint_label.alignment = "center"

        self.add_children(
            [self.not_visible_icon, self.visible_icon, self.title_label, self.hint_label]
        )

    def is_visible(self):
        self.not_visible_icon.visible = False
        for i in range(len(self.children)):
            if self.children[i]._id == self.not_visible_icon._id:
                self.children[i].visible = False
                break
        self.root_widget_tree.update(self.not_visible_icon)
        self.visible_icon.visible = True
        for i in range(len(self.children)):
            if self.children[i]._id == self.visible_icon._id:
                self.children[i].visible = True
                break
        self.root_widget_tree.update(self.visible_icon)
        self.root_widget_tree.update(self)

    def not_visible(self):
        self.not_visible_icon.visible = True
        for i in range(len(self.children)):
            if self.children[i]._id == self.not_visible_icon._id:
                self.children[i].visible = True
                break
        self.root_widget_tree.update(self.not_visible_icon)
        self.visible_icon.visible = False
        for i in range(len(self.children)):
            if self.children[i]._id == self.visible_icon._id:
                self.children[i].visible = False
                break
        self.root_widget_tree.update(self.visible_icon)
        self.root_widget_tree.update(self)


class RightHandCalibrationScreen(Container):
    def __init__(self, rect=[0, 0, 1920, 918], on_complete=None, radius=0, border_color=None):
        super().__init__([0, 0, 1920, 1080])
        
        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name="Hand Movement",
                                                           task_icon=read_asset_image("hand.png")))
        self.content = Container([0, 100, 1920, 918])
        self.content.corner_radius = radius
        self.content.border_color = border_color
        self.content.alpha = 0

        self.right_title_label = Label(
            [0, 41, 1920, 57],
            data=messages["right_hand_calib_title"],
        )
        self.right_title_label.font_size = 46.67
        self.right_title_label.font_weight = "bold"
        self.right_title_label.alignment = "center"

        self.description_label = Label(
            [0, 105, 1920, 44],
            data=messages["face_right_hand_hint"],
        )
        self.description_label.font_size = 36
        self.description_label.alignment = "center"

        self.right_hand_visibility_container = HandVisibilityContainer(
            [712.5, 180, 495, 104], "Right"
        )

        self.main_camera_image = Image(
            [473, 338.85, 975.45, 549.15]
        )

        self.on_complete = on_complete

        self.skip_right_hand_button = Button(
            [1689, 824, 200.67, 64]
        )
        self.skip_right_hand_button.label = messages["skip_right_hand"]
        self.skip_right_hand_button.button_color = "#FFC700"
        self.skip_right_hand_button.label_font_color = "#000000"
        self.skip_right_hand_button.label_font_size = 20
        self.skip_right_hand_button.on_widget_event = self.on_complete

        self.complete_calibration_button = Button(
            [1689, 824, 200.67, 64]
        )
        self.complete_calibration_button.label = messages["complete"]
        self.complete_calibration_button.button_color = "#FFC700"
        self.complete_calibration_button.label_font_color = "#000000"
        self.complete_calibration_button.label_font_size = 20
        self.complete_calibration_button.visible = False

        self.content.add_children([self.right_title_label, 
                           self.description_label, 
                           self.right_hand_visibility_container, 
                           self.main_camera_image, 
                           self.skip_right_hand_button, 
                           self.complete_calibration_button])
        
        self.task_background.update_content(self.content)
        self.add_child(self.task_background)
