from cortic_platform.sdk.ui.basic_widgets import Container, Image, Label
from common_screen_element import background_image
from common_utils import read_asset_image


class MainBackgroundContainer(Container):
    def __init__(self, rect=[0, 0, 1920, 1080], radius=0, border_color="#ffffff"):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.alignment = "center"
        self.background_image = background_image
        self.background_image.scaling_mode = "fill"

        self.copyright_label = Label(
            [20, 1038, 300, 22], data="Copyright \\u00a9 2023, The McKeown Lab"
        )
        self.cortic_label = Label([1665.97, 1038, 73, 22], data="Powerd by")
        self.cortic_logo = Image(
            [1746.97, 1038.81, 153.03, 20.39],
            data=read_asset_image("cortic_horizontal.png"),
        )
        self.add_children([self.background_image, self.copyright_label, self.cortic_label, self.cortic_logo])
