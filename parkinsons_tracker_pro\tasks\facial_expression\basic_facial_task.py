import time
import os
from base_task import BaseTask
from screens.common_screen_element import TaskTitle
from screens.video_instruction_screen import VideoInstructionScreen
from screens.facial_expressions.video_feed_screen import VideoFeedScreen
from screens.facial_expressions.basic_facial_describe_screen import (
    BasicFacialDescribeScreen,
)
from task_data import instructions, messages
from service_registry import *
from pathlib import Path
import sys
from pygame import mixer
from task_data import messages
import json
from cortic_platform.sdk.app_events import ExceptionTypes

mixer.init()


class BasicFacialTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("basic_facial", module, on_task_complete, task_params)
        self.temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/temp_collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/basic_facial/"
        )
        self.collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/basic_facial/"
        )
        Path(self.temp_collected_data_path).mkdir(parents=True, exist_ok=True)
        self.module.session.logger.info(
            "Created temp data collection dir for basic facial task"
        )
        # os.makedirs(os.path.dirname(self.collected_data_path), exist_ok=True)
        self.beep_effect = mixer.Sound(
            os.path.dirname(os.path.realpath(__file__)) +
            "/../../assets/beep.mp3"
        )
        self.end_beep_effect = mixer.Sound(
            os.path.dirname(os.path.realpath(__file__))
            + "/../../assets/end_beep.mp3"
        )
        self.title = TaskTitle(
            [800, 32, 571, 42], self.module.name, self.module.icon)
        self.screens = [
            VideoInstructionScreen(
                [200, 90, 1520, 950],
                instructions["neutral_facial_expression"],
                self.module.resources["Facial Expression Basics_Neutral.mp4"],
                subtitle="Neutral Facial Expression",
                font_size=28,
                on_continue=self.next_screen,
                text_height=130,
            ),
            VideoFeedScreen(
                [200, 90, 1720, 950],
                instruction_text=messages["press_start_hint"],
                subtitle="Neutral Facial Expression",
                expression_type="neutral_facial_expression",
                font_size=28,
                on_start_record=self.start_recording,
                on_stop_record=self.stop_recording,
            ),
            #            VideoInstructionScreen(
            #                [200, 90, 1520, 950],
            #                instructions["raising_eyebrows"],
            #                self.module.resources["Facial Expression Basics_Raise_Eyebrows.mp4"],
            #                subtitle="Raising Eyebrows",
            #                font_size=28,
            #                on_continue=self.next_screen,
            #            ),
            #            VideoFeedScreen(
            #                [200, 90, 1720, 950],
            #                instruction_text=messages["press_start_hint"],
            #                subtitle="Raising Eyebrows",
            #                expression_type="raising_eyebrows",
            #                font_size=28,
            #                on_start_record=self.start_recording,
            #                on_stop_record=self.stop_recording,
            #            ),
            #            VideoInstructionScreen(
            #                [200, 90, 1520, 950],
            #                instructions["closing_eyes"],
            #                self.module.resources["Facial Expression Basics_Eyeclosure.mp4"],
            #                subtitle="Closing Eyes",
            #                font_size=28,
            #                on_continue=self.next_screen,
            #            ),
            #            VideoFeedScreen(
            #                [200, 90, 1720, 950],
            #                instruction_text=messages["press_start_hint"],
            #                subtitle="Closing Eyes",
            #                expression_type="closing_eyes",
            #                font_size=28,
            #                on_start_record=self.start_recording,
            #                on_stop_record=self.stop_recording,
            #            ),
            #            VideoInstructionScreen(
            #                [200, 90, 1520, 950],
            #                instructions["big_smile"],
            #                self.module.resources["Facial Expression Basics_ShowTeeth.mp4"],
            #                subtitle="Big Smile",
            #                font_size=28,
            #                on_continue=self.next_screen,
            #            ),
            #            VideoFeedScreen(
            #                [200, 90, 1720, 950],
            #                instruction_text=messages["press_start_hint"],
            #                subtitle="Big Smile",
            #                expression_type="big_smile",
            #                font_size=28,
            #                on_start_record=self.start_recording,
            #                on_stop_record=self.stop_recording,
            #            ),
            #            BasicFacialDescribeScreen(
            #                [200, 90, 1720, 950],
            #                instruction_text=instructions["facial_expression_during_speech"],
            #                image_url=self.module.resources["Cookie_Theft_Color.png"],
            #                subtitle="Facial Expression During Speech",
            #                expression_type="facial_expression_during_speech",
            #                image_width=520,
            #                image_height=375.75,
            #                on_start_record=self.start_recording,
            #                on_stop_record=self.stop_recording,
            #            ),
        ]
        self.video_start_time = None
        self.video_record_time = 10
        self.num_seconds = 0
        self.start_record = False
        self.module.session.logger.info(
            "("
            + self.module.session.patient_id
            + ") "
            + self.name
            + " task initialized"
        )

    def next_screen(self, data=None):
        self.current_screen_index += 1
        if self.current_screen_index >= len(self.screens):
            self.on_task_complete("basic_facial")
        else:
            self.current_content = self.screens[self.current_screen_index]
            self.module.session.update_current_task_screen(self.current_content)

    def start_recording(self, data):
        self.current_content.show_stop_record_button()
        self.start_record = True

    def stop_recording(self, data):
        self.current_content.show_start_record_button()
        self.current_content.stat_container.time_meter.data = 0
        self.num_seconds = 0
        self.video_start_time = None
        self.start_record = False
        oakd_capture({"reset_video": True, "video_file_name": ""}).get_data()

    def process(self):
        if self.is_running:
            if isinstance(self.current_content, VideoFeedScreen) or isinstance(
                self.current_content, BasicFacialDescribeScreen
            ):
                if self.start_record:
                    if self.video_start_time is None:
                        # self.beep_effect.play()
                        self.video_start_time = time.time()
                        elapsed_time = 0
                    else:
                        elapsed_time = time.time() - self.video_start_time
                        if elapsed_time >= 1:
                            self.beep_effect.play()
                            self.num_seconds += 1
                            self.video_start_time = time.time()
                            elapsed_time = 0

                        self.current_content.stat_container.time_meter.data = (
                            self.num_seconds + elapsed_time
                        )
                    if self.num_seconds >= self.video_record_time:
                        self.end_beep_effect.play()
                        self.module.session.logger.info(
                            self.name
                            + " task ("
                            + self.current_content.expression_type
                            + ")"
                            + " complete, checking if data is recorded..."
                        )
                        self.video_start_time = None
                        self.num_seconds = 0
                        self.stop_recording(True)
                        if not os.path.isfile(
                            self.temp_collected_data_path
                            + self.current_content.expression_type
                            + ".h264"
                        ):
                            self.module.session.logger.error(
                                "Video File: "
                                + self.current_content.expression_type
                                + " for task "
                                + self.name
                                + " is not found, raising exception"
                            )
                            self.module.session.logger.error(
                                self.name + " task recorded data not found"
                            )
                            sys.stderr.write(
                                json.dumps(
                                    {
                                        "app_exception_msg": self.name
                                        + ": "
                                        + messages["recordedDataNotFound"]
                                    }
                                )
                                + "\n"
                            )
                            raise Exception(messages["recordedDataNotFound"])
                        else:
                            self.module.session.logger.info(
                                self.name + " task recorderd data found..."
                            )
                        self.next_screen()
                    else:
                        frame_data = oakd_capture({"reset_video": False,
                                                   "video_file_name": self.current_content.expression_type},
                                                  service_states={"action": "capture",
                                                                  "save_path": self.temp_collected_data_path})
                        task_data = None
                        if frame_data is not None:
                            face_landmarks_result = face_landmarks(
                                {"camera_input": frame_data,
                                 "draw_landmarks": True},
                                service_states={"draw_mesh_only": True})
                            if face_landmarks_result is not None and not isinstance(face_landmarks_result, ExceptionTypes):
                                task_data = face_landmarks_result.get_data()
                        if task_data:
                            self.current_content.camera_image.set_data(
                                task_data["frame"])
                else:
                    frame_data = oakd_capture({"reset_video": False,
                                               "video_file_name": ""},
                                              service_states={"action": "capture"})
                    task_data = None
                    if frame_data is not None:
                        face_landmarks_result = face_landmarks(
                            {"camera_input": frame_data,
                                "draw_landmarks": True},
                            service_states={"draw_mesh_only": True})
                        if face_landmarks_result is not None and not isinstance(face_landmarks_result, ExceptionTypes):
                            task_data = face_landmarks_result.get_data()
                    if task_data is not None:
                        self.current_content.camera_image.set_data(
                            task_data["frame"])
