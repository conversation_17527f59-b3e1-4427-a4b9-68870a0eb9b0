from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image
from cortic_platform.sdk.ui.input_widgets import Button
from cortic_platform.sdk.ui.misc_widgets import CircularCounter
from utils import read_asset_image
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)) + "/../..")
from screens.task_background_screen import TaskBackgroundContainer
from screens.common_screen_element import TaskTitle
from task_data import messages


class VideoStatContainer(Container):
    def __init__(self, rect=[0, 0, 180, 180], radius=0, border_color=None):
        super().__init__(rect)
        self.border_color = border_color
        self.corner_radius = 20
        self.alpha = 1

        self.time_label = Label(
            [0, 15, 180, 37],
            data=messages["time"],
        )
        self.time_label.font_size = 30
        self.time_label.alignment = "center"
        self.time_label.font_weight = "bold"

        self.time_meter = CircularCounter(
            [35, 55, 112, 112]
        )
        self.time_meter.radius = 50
        self.time_meter.counter_font_size = 64
        self.time_meter.end_value = 10
        self.time_meter.ring_thickness = 9

        self.add_children(
            [self.time_label, self.time_meter]
        )


class VideoFeedScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1720, 950],
        instruction_text="Instruction Text",
        subtitle="Subtitle",
        expression_type="expression_type",
        text_width=1023,
        text_height=80,
        font_size=30,
        radius=0,
        border_color=None,
        on_start_record=None,
        on_stop_record=None,
    ):
        super().__init__([0, 0, 1920, 1080])
        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name="Facial Expression",
                                                           task_icon=read_asset_image("facial.png")))
        
        self.content = Container([200, 90, 1720, 950])
        self.content.border_color = border_color
        self.content.corner_radius = radius
        self.content.alpha = 0

        self.expression_type = expression_type

        self.subtitle = Label(
            [248.5, 15, 1023, 60],
            data=subtitle
        )
        self.subtitle.font_size = 40
        self.subtitle.alignment = "center"
        self.subtitle.font_weight = "bold"

        self.instruction_text = Label(
            [248.5, 70, text_width, text_height],
            data=instruction_text
        )
        self.instruction_text.font_size = font_size
        self.instruction_text.alignment = "left"
        self.instruction_text.font_weight = "thin"

        self.stat_container = VideoStatContainer([1520, 25, 180, 180])

        self.camera_image = Image([248.5, 160, 1023, 578])
        self.camera_image.alpha = 1
        self.camera_image.corner_radius = 10
        self.camera_image.border_color = "#000000"

        self.start_record_button = Button(
            [660, 160 + 578 + 190 / 2 - 64 / 2, 200, 64]
        )
        self.start_record_button.label = "Start Recording"
        self.start_record_button.label_font_size = 22
        self.start_record_button.on_widget_event = on_start_record

        self.stop_record_button = Button(
            [660, 160 + 578 + 190 / 2 - 64 / 2, 200, 64]
        )
        self.stop_record_button.visible = False
        self.stop_record_button.label = "Stop Recording"
        self.stop_record_button.label_font_size = 22
        self.stop_record_button.on_widget_event = on_stop_record

        self.content.add_children(
            [self.stat_container, self.subtitle, self.instruction_text, self.camera_image, self.start_record_button, self.stop_record_button]
        )

        self.task_background.update_content(self.content)
        self.add_child(self.task_background)

    def show_start_record_button(self):
        self.start_record_button.visible = True
        self.root_widget_tree.update(self.start_record_button)
        self.stop_record_button.visible = False
        self.root_widget_tree.update(self.stop_record_button)

    def show_stop_record_button(self):
        self.start_record_button.visible = False
        self.root_widget_tree.update(self.start_record_button)
        self.stop_record_button.visible = True
        self.root_widget_tree.update(self.stop_record_button)
