import json
import requests
from config import Config


class AwsAuthentication:
    def __init__(self):
        self.cloud_region = "ca-central-1"
        self.app_client_id = Config.app_client_id
        self.headers = {
            "X-Amz-Target": "AWSCognitoIdentityProviderService.InitiateAuth",
            "Content-type": "application/x-amz-json-1.1",
        }

    def login(self, username, password):
        data = {
            "AuthParameters": {"USERNAME": username, "PASSWORD": password},
            "AuthFlow": "USER_PASSWORD_AUTH",
            "ClientId": self.app_client_id,
        }

        response = requests.post(
            "https://cognito-idp." + self.cloud_region + ".amazonaws.com/",
            headers=self.headers,
            json=data,
        )

        if response.ok:
            return True, response.json()["AuthenticationResult"]["AccessToken"]
        else:
            return False, response.json()["message"]
