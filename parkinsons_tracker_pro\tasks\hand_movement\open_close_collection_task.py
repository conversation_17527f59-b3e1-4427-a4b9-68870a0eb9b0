from base_task import BaseTask
from screens.common_screen_element import TaskTitle
from screens.hand_movement.open_close_collection_screen import OpenClose<PERSON>ollectionScreen
import os
import json
import time
from service_registry import *
import sys
from pathlib import Path
from task_data import messages
from cortic_platform.sdk.app_events import ExceptionTypes

class OpenCloseCollectionTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("open_close_collection", module, on_task_complete, task_params)
        with open(
            os.path.dirname(os.path.realpath(__file__)) + "/parameters.json"
        ) as f:
            self.params = json.load(f)
        self.current_hand = task_params
        self.temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/temp_collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/"
            + self.current_hand
            + "_open_close_collection/"
        )
        self.collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/"
            + self.current_hand
            + "_open_close_collection/"
        )
        Path(self.temp_collected_data_path).mkdir(parents=True, exist_ok=True)
        self.module.session.logger.info(
            "Created temp data collection dir for "
            + self.current_hand
            + " hand open close task"
        )
        self.title = TaskTitle(
            [840, 32, 571, 42], self.module.name, self.module.icon)
        self.screens = [OpenCloseCollectionScreen([0, 100, 1920, 918])]
        self.close_once = False
        self.num_open_close = 0
        self.num_seconds = 0
        self.open_close_reset = True
        self.start_time = None
        self.target_taps = 15
        self.target_seconds = 20
        oakd_capture({"reset_video": True, "video_file_name": ""}).get_data()
        self.module.session.logger.info(
            "("
            + self.module.session.patient_id
            + ") "
            + self.name
            + " task initialized"
        )

    def count_open_close(self, avg_finger_distance):
        if self.open_close_reset:
            if avg_finger_distance < self.params["close_threshold"]:
                self.num_open_close += 1
                self.open_close_reset = False
                num_open_close = self.num_open_close
                self.current_content.stat_container.taps_meter.data = num_open_close
        if avg_finger_distance > self.params["close_deadzone"]:
            if not self.open_close_reset:
                self.open_close_reset = True

    def get_avg_finger_distance(self, hand_infos):
        if len(hand_infos) == 0:
            return -1
        hand_info = None
        for hand in hand_infos:
            if hand["label"] == self.current_hand:
                hand_info = hand
        if hand_info is None:
            return -1
        return hand_info["avg_finger_distance"]

    def process(self):
        if isinstance(self.current_content, OpenCloseCollectionScreen):
            if self.num_open_close >= self.target_taps or self.num_seconds >= self.target_seconds:
                oakd_capture({"reset_video": True, "video_file_name": ""})
                self.module.session.logger.info(
                    self.name + " task complete, checking if data is recorded..."
                )
                if not os.path.isfile(
                    self.temp_collected_data_path
                    + self.current_hand
                    + "_open_close"
                    + ".h264"
                ):
                    self.module.session.logger.error(
                        "Video File for task "
                        + self.name
                        + " is not found, raising exception"
                    )
                    self.module.session.logger.error(
                        self.name + " task recorded data not found"
                    )
                    sys.stderr.write(
                        json.dumps(
                            {
                                "app_exception_msg": self.name
                                + ": "
                                + messages["recordedDataNotFound"]
                            }
                        )
                        + "\n"
                    )
                    raise Exception(messages["recordedDataNotFound"])
                else:
                    self.module.session.logger.info(
                        self.name + " task recorderd data found..."
                    )
                self.on_task_complete(
                    self.current_hand + "_open_close_collection")
            else:
                frame_data = oakd_capture({"reset_video": False, "video_file_name": self.current_hand + "_open_close"},
                                          service_states={"action": "capture",
                                                          "save_path": self.temp_collected_data_path})
                task_data = None
                if frame_data is not None:
                    finger_tapping_data = finger_tapping_detector(
                        {"camera_input": frame_data})
                    if finger_tapping_data is not None and not isinstance(finger_tapping_data, ExceptionTypes):
                        task_data = finger_tapping_data.get_data()
                if task_data:
                    self.current_content.main_camera_image.data = task_data["frame"]
                    avg_finger_distance = self.get_avg_finger_distance(
                        task_data["hand_infos"]
                    )
                    if not self.close_once:
                        if avg_finger_distance != -1:
                            if avg_finger_distance < self.params["close_threshold"]:
                                self.close_once = True
                                self.open_close_reset = False
                                self.current_content.start_statistic()
                                self.start_time = time.time()
                    else:
                        elapsed_time = time.time() - self.start_time
                        if elapsed_time >= 1:
                            self.num_seconds += 1
                            self.start_time = time.time()
                            elapsed_time = 0
                        self.current_content.stat_container.time_meter.data = (
                            self.num_seconds + elapsed_time
                        )
                        if avg_finger_distance != -1:
                            self.count_open_close(avg_finger_distance)
