import time
import os
import cv2
import sys
from cortic_platform.sdk.app_events import AppException
from base_task import BaseTask
from screens.common_screen_element import InstructionTitle
from screens.text_instruction_screen import TextInstructionScreen
from screens.eye_movement.calibration_screen import (
    CalibrationScreen,
)
from task_data import instructions, messages
from config import Config
import numpy as np
import random
import threading
import json
from service_registry import *
from cortic_platform.sdk.app_events import ExceptionTypes


class CalibrationTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("calibration", module, on_task_complete, task_params)
        with open(
            os.path.dirname(os.path.realpath(__file__)) + "/parameters.json"
        ) as f:
            self.params = json.load(f)
        self.temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/temp_collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/calibration/"
        )
        self.collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/calibration/"
        )
        os.makedirs(os.path.dirname(
            self.temp_collected_data_path), exist_ok=True)
        self.module.session.logger.info(
            "Created temp data collection dir for eye calibration task"
        )
        # os.makedirs(os.path.dirname(self.collected_data_path), exist_ok=True)
        print("Created temp and collected data dir")
        self.initialized = False
        self.capture_frame = False
        self.face_normal = "Need calibration"
        self.calibrated = False
        self.showed_finish_symbol = False
        self.calibration_ok_time = time.time()
        self.hold_on_time = 1
        self.calibration_finish_time = time.time()
        self.show_finishing_symbol_time = 1
        self.x_angle_range = 16
        self.y_angle_range = 16
        self.center_region_width = 500
        self.center_region_height = 500
        self.head_pose_calibrated = False
        self.switching_pipeline = False
        self.facelandmark_collection_count = 0
        self.num_landmarks_to_collect = 5
        self.start_head_pose_count_down = False
        self.start_time = None
        self.head_pose_calibration_time = 7
        self.iris_calibration_time = 3
        self.landmarks_2d = None
        self.landmarks_3d = None
        self.current_iris_target = None
        self.current_iris_target_name = ""
        self.iris_focused_on_target = False
        self.iris_targets = []
        self.ref_right_eye = None
        self.ref_left_eye = None
        self.head_pose_is_front = True
        self.target_start_time = None
        self.head_pose_threshold = 5
        self.head_pose_vertical_threshold = 7
        self.previous_left_eye_x = 0
        self.previous_left_eye_y = 0
        self.previous_right_eye_x = 0
        self.previous_right_eye_y = 0
        self.smooth_factor = 0.2
        self.iris_location_on_target = {}
        self.calibration_ok_time = None
        self.calibration_ok_count = 2
        self.target_appear_time = None
        self.face_missing_start_time = None
        self.cancel_time = 5
        self.exception_retry_attempt = 0
        self.title = InstructionTitle([224, 0, 1471.76, 100], "Calibration")
        self.screens = [
            TextInstructionScreen(
                [200, 90, 1520, 950],
                instruction_text=instructions["eye_calibration"],
                subtitle="Calibration",
                font_size=45,
                on_continue=self.next_screen,
            )
        ]
        self.calibration_location_index = -1
        self.calibration_locations = []
        calibration_screen = CalibrationScreen(
            [0, 0, 1920, 1080],
            self.center_region_width,
            self.center_region_height,
            head_pose_calibration_time=self.calibration_ok_count,
            circular_region_diameter=190,
            iris_target_size=Config.calibration_target_radius * 2,
        )
        self.screens.append(calibration_screen)
        enlarged_center_width = self.center_region_width * 1.45
        enlarged_center_height = calibration_screen.region_height * 1.25
        self.target_regions = {
            0: {
                "x": [
                    0,
                    Config.monitor_width_px // 2
                    - enlarged_center_width // 2
                    - 2 * Config.calibration_target_radius,
                ],
                "y": [
                    0,
                    Config.monitor_height_px // 2
                    - enlarged_center_height // 2
                    - 2 * Config.calibration_target_radius,
                ],
                "x_criteria": self.params["calibration_region_1_x_scale"],
                "use_right_eye": True,
            },
            1: {
                "x": [
                    Config.monitor_width_px // 2
                    - enlarged_center_width // 3
                    - enlarged_center_width,
                    Config.monitor_width_px // 2
                    - enlarged_center_width // 3
                    - 2 * Config.calibration_target_radius,
                ],
                "y": [
                    0,
                    Config.monitor_height_px // 2
                    - enlarged_center_height // 2
                    - 2 * Config.calibration_target_radius,
                ],
                "x_criteria": self.params["calibration_region_2_x_scale"],
                "use_right_eye": True,
            },
            2: {
                "x": [
                    Config.monitor_width_px // 2 + enlarged_center_width // 2,
                    Config.monitor_width_px - 2 * Config.calibration_target_radius,
                ],
                "y": [
                    0,
                    Config.monitor_height_px // 2
                    - enlarged_center_height // 2
                    - 2 * Config.calibration_target_radius,
                ],
                "x_criteria": self.params["calibration_region_3_x_scale"],
                "y_criteria": -1,
                "use_right_eye": True,
            },
            3: {
                "x": [
                    0,
                    Config.monitor_width_px // 2
                    - enlarged_center_width // 2
                    - 2 * Config.calibration_target_radius,
                ],
                "y": [
                    Config.monitor_height_px // 2 - enlarged_center_height // 2,
                    Config.monitor_height_px // 2
                    + enlarged_center_height // 2
                    - 2 * Config.calibration_target_radius,
                ],
                "x_criteria": self.params["calibration_region_4_x_scale"],
                "use_right_eye": True,
            },
            4: {
                "x": [
                    Config.monitor_width_px // 2 + enlarged_center_width // 2,
                    Config.monitor_width_px - 2 * Config.calibration_target_radius,
                ],
                "y": [
                    Config.monitor_height_px // 2 - enlarged_center_height // 2,
                    Config.monitor_height_px // 2
                    + enlarged_center_height // 2
                    - 2 * Config.calibration_target_radius,
                ],
                "x_criteria": self.params["calibration_region_5_x_scale"],
                "use_right_eye": True,
            },
            5: {
                "x": [
                    0,
                    Config.monitor_width_px // 2
                    - enlarged_center_width // 2
                    - 2 * Config.calibration_target_radius,
                ],
                "y": [
                    Config.monitor_height_px // 2 + enlarged_center_height // 2,
                    Config.monitor_height_px - 2 * Config.calibration_target_radius,
                ],
                "x_criteria": self.params["calibration_region_6_x_scale"],
                "y_criteria": 1,
                "use_right_eye": True,
            },
            6: {
                "x": [
                    Config.monitor_width_px // 2 + enlarged_center_width // 2,
                    Config.monitor_width_px - 2 * Config.calibration_target_radius,
                ],
                "y": [
                    Config.monitor_height_px // 2 + enlarged_center_height // 2,
                    Config.monitor_height_px - 2 * Config.calibration_target_radius,
                ],
                "x_criteria": self.params["calibration_region_7_x_scale"],
                "y_criteria": 1,
                "use_right_eye": False,
            },
            7: {
                "x": [
                    Config.monitor_width_px // 2 + enlarged_center_width // 2,
                    Config.monitor_width_px - 2 * Config.calibration_target_radius,
                ],
                "y": [
                    Config.monitor_height_px // 2 + enlarged_center_height // 2,
                    Config.monitor_height_px - 2 * Config.calibration_target_radius,
                ],
                "x_criteria": self.params["calibration_region_8_x_scale"],
                "y_criteria": 1,
                "use_right_eye": True,
            },
        }
        self.generate_target_locations()
        self.video_meta = {}
        for i in range(len(self.calibration_locations)):
            self.video_meta["calibration_video_" + str(i)] = {
                "x": self.calibration_locations[i][0],
                "y": self.calibration_locations[i][1],
            }
        self.initialized = True
        self.camera_params = oakd_capture({"reset_video": True,
                                           "video_file_name": ""}).get_data()
        self.module.session.logger.info(
            "("
            + self.module.session.patient_id
            + ") "
            + self.name
            + " task initialized"
        )

    def generate_target_locations(self):
        h = Config.monitor_height_px - 2 * Config.calibration_target_radius
        w = Config.monitor_width_px - 2 * Config.calibration_target_radius

        for i in range(14):
            if i < 4:
                if i == 0:
                    region = 0
                if i == 1:
                    region = 2
                if i == 2:
                    region = 5
                if i == 3:
                    region = 7
                row = i % 2
                col = int(i / 2)
                x = int(row * w)
                y = int(col * h)
            else:
                region = random.randint(1, 7)
                # while region == 1:
                #     rregion = random.randint(0, 7)
                x = random.randint(
                    self.target_regions[region]["x"][0],
                    self.target_regions[region]["x"][1],
                )
                y = random.randint(
                    self.target_regions[region]["y"][0],
                    self.target_regions[region]["y"][1],
                )

            self.calibration_locations.append([x, y, region])

    def next_screen(self, data=None):
        if self.current_screen_index == 0:
            self.module.session.loading_background.visible = True
            self.module.session.widget_tree.update(
                self.module.session.loading_background
            )
            oakd_capture({"reset_video": False,
                          "video_file_name": ""},
                         service_states={"action": "switch_to_depth"}).get_data()
            print("Done switch to depth mode")
            
        self.current_screen_index += 1
        if self.current_screen_index >= len(self.screens):
            self.capture_frame = False
            self.module.session.loading_background.visible = True
            self.module.session.widget_tree.update(
                self.module.session.loading_background
            )
            oakd_capture({"reset_video": False,
                          "video_file_name": ""},
                         service_states={"action": "switch_to_rgb"}).get_data()
            self.module.session.loading_background.visible = False
            self.module.session.widget_tree.update(
                self.module.session.loading_background
            )
            self.on_task_complete("calibration")
        else:
            self.current_content = self.screens[self.current_screen_index]
            if isinstance(self.current_content, CalibrationScreen):
                self.capture_frame = True
                # self.module.background.update_content(None, from_buffer_content=True)
            self.module.session.loading_background.visible = False
            self.module.session.widget_tree.update(
                self.module.session.loading_background
            )
            self.module.session.update_current_task_screen(self.current_content)
            
    def get_face_angles(self, rvec, tvec):
        try:
            rotation_mat, _ = cv2.Rodrigues(np.array(rvec))
            pose_mat = cv2.hconcat((rotation_mat, np.array(tvec)))
            _, _, _, _, _, _, euler_angles = cv2.decomposeProjectionMatrix(
                pose_mat)
            return euler_angles
        except:
            print(rvec, tvec)
            return [100, 100, 100]

    def within_target_circle(self, x1, y1, x2, y2, r1, r2):
        distSq = (((x1 - x2) * (x1 - x2)) + ((y1 - y2) * (y1 - y2))) ** (0.5)
        if distSq + r2 <= r1:
            return True
        else:
            return False

    def setup_iris_calibration_func(self, landmarks_2d, landmarks_3d):
        oakd_capture({"reset_video": False,
                      "video_file_name": ""},
                     service_states={"action": "switch_to_rgb"})
        filename = self.temp_collected_data_path + "front_face_2d_landmarks.json"
        os.makedirs(os.path.dirname(
            self.temp_collected_data_path), exist_ok=True)
        with open(
            filename,
            "w+",
        ) as f:
            f.write(json.dumps({"2d_face_landmarks": landmarks_2d}))
        self.module.session.logger.info(
            self.name + " task: saved front_face_2d_landmarks.json"
        )
        filename = self.temp_collected_data_path + "front_face_3d_landmarks.json"
        with open(
            filename,
            "w+",
        ) as f:
            f.write(json.dumps(
                {"3d_face_landmarks": landmarks_3d["landmarks_3d"]}))
        self.module.session.logger.info(
            self.name + " task: saved front_face_3d_landmarks.json"
        )

    def get_2d_3d_landmarks(self, face_landmarks):
        calibrated_normal_face_2d_landmark = np.array(face_landmarks)
        calibrated_normal_face_2d_landmark[:, 0] = (
            calibrated_normal_face_2d_landmark[:,
                                               0] * self.camera_params["frame_width"]
        )
        calibrated_normal_face_2d_landmark[:, 1] = (
            calibrated_normal_face_2d_landmark[:, 1]
            * self.camera_params["frame_height"]
        )
        calibrated_normal_face_2d_landmark[:, 0] = (
            calibrated_normal_face_2d_landmark[:, 0]
            - self.camera_params["frame_width"] / 2
        )
        calibrated_normal_face_2d_landmark[:, 1] = (
            self.camera_params["frame_height"] / 2
            - calibrated_normal_face_2d_landmark[:, 1]
        )
        frame_data = oakd_capture({"reset_video": False,
                                   "video_file_name": ""},
                                  service_states={"action": "depth",
                                                  "landmarks": face_landmarks})
        landmarks_3d = []
        if frame_data is not None and not isinstance(frame_data, ExceptionTypes):
            landmarks_3d = frame_data.get_data()
        return calibrated_normal_face_2d_landmark.tolist(), landmarks_3d

    def calculate_target_offset(self, face_angles):
        x_offset_portion = face_angles[1][0] / self.x_angle_range
        y_offset_portion = face_angles[0][0] / self.y_angle_range
        if x_offset_portion > 1:
            x_offset_portion = 1
        if x_offset_portion < -1:
            x_offset_portion = -1
        if y_offset_portion > 1:
            y_offset_portion = 1
        if y_offset_portion < -1:
            y_offset_portion = -1
        x_offset = (
            self.current_content.region_width / 2
            - self.current_content.circular_region_diameter / 4
        ) + x_offset_portion * (
            self.current_content.region_width / 2
            - self.current_content.circular_region_diameter / 4
        )
        y_offset = (
            self.current_content.region_height / 2
            - self.current_content.circular_region_diameter / 4
        ) + y_offset_portion * (
            self.current_content.region_height / 2
            - self.current_content.circular_region_diameter / 4
        )
        return x_offset, y_offset

    def is_face_normal(self, rvec, tvec, from_iris=False):
        within_range = False
        face_angles = [100, 100, 100]
        difference = 100
        message = messages["no_face"]
        if len(rvec) > 0 and len(tvec) > 0:
            rotation_mat, _ = cv2.Rodrigues(np.array(rvec))
            pose_mat = cv2.hconcat((rotation_mat, np.array(tvec)))
            _, _, _, _, _, _, euler_angles = cv2.decomposeProjectionMatrix(
                pose_mat)
            face_angles = euler_angles.flatten()
            target_angles = np.array([0, 0, 0])
            if from_iris:
                target_angles = np.array([180, 0, 0])
            difference = face_angles - target_angles
            within_range_1 = np.all(
                np.abs(difference)[1:3] <= self.head_pose_threshold - 0.5
            )
            within_range = (
                within_range_1
                and np.abs(difference)[0] <= self.head_pose_vertical_threshold - 0.5
            )
            if not within_range:
                if abs(difference[1]) > self.head_pose_threshold:
                    if difference[1] > 0:
                        message = messages["rotate_left"]
                    else:
                        message = messages["rotate_right"]
                elif abs(difference[0]) > self.head_pose_vertical_threshold:
                    if from_iris:
                        if face_angles[0] < 0:
                            message = messages["rotate_up"]
                        else:
                            message = messages["rotate_down"]
                    else:
                        if difference[0] > 0:
                            message = messages["rotate_up"]
                        else:
                            message = messages["rotate_down"]
                elif abs(difference[2]) > self.head_pose_threshold:
                    if difference[2] > 0:
                        message = messages["rotate_clockwise"]
                    else:
                        message = messages["rotate_cclockwise"]
            else:
                message = messages["ok"]

        return within_range, message, face_angles, difference

    def get_target_location(self, difference):
        x_offset = difference[1] / (
            (
                2
                * self.current_content.x1
                / (self.current_content.circular_region_diameter / 6 * 2 + 20)
            )
            * self.head_pose_threshold
        )
        if x_offset < -1:
            x_offset = -1

        x_offset = self.current_content.x1 - \
            x_offset * (self.current_content.x1)
        if x_offset >= (2 * self.current_content.x1 - 2 * self.current_content.r2):
            x_offset = 2 * self.current_content.x1 - self.current_content.r2
        x_offset = x_offset - self.current_content.r2
        if x_offset < 0:
            x_offset = 0
        y_offset = difference[0] / (
            (
                2
                * (85 + self.current_content.line_step)
                / (self.current_content.circular_region_diameter / 6 * 2 + 20)
            )
            * self.head_pose_vertical_threshold
        )
        if y_offset < -1:
            y_offset = -1
        y_offset = (
            85
            + self.current_content.line_step
            + y_offset * (85 + self.current_content.line_step)
        )
        y_offset = y_offset - self.current_content.r2
        if y_offset < 0:
            y_offset = 0
        return x_offset, y_offset

    def check_face_center(self, face_location):
        is_center = True
        message = messages["ok"]
        if len(face_location) > 0:
            if face_location[0] < 0.3:
                is_center = False
                message = messages["move_left"]
            if face_location[2] > 0.7:
                is_center = False
                message = messages["move_right"]
            if face_location[1] < 0.01:
                is_center = False
                message = messages["move_lower"]
            if face_location[3] > 0.99:
                is_center = False
                message = messages["move_upper"]
        return is_center, message, len(face_location)

    def process(self):
        if isinstance(self.current_content, CalibrationScreen):
            if not self.head_pose_calibrated:
                if not self.switching_pipeline:
                    frame_data = oakd_capture({"reset_video": False,
                                               "video_file_name": ""},
                                              service_states={"action": "capture"})
                    task_data = None
                    if frame_data is not None:
                        head_pose_result = head_pose_estimation(
                            {"camera_input": frame_data})
                        if head_pose_result is not None and not isinstance(head_pose_result, ExceptionTypes):
                            task_data = head_pose_result.get_data()

                    if task_data:
                        if isinstance(task_data, ExceptionTypes):
                            print("Exception: ", task_data)
                            return
                        else:
                            if "frame" in task_data:
                                self.current_content.head_pose_image.set_data(
                                    task_data["frame"])
                            if len(task_data["face_location"]) > 0:
                                if self.face_missing_start_time is not None:
                                    self.face_missing_start_time = None
                                (
                                    is_face_center,
                                    message,
                                    num_face,
                                ) = self.check_face_center(task_data["face_location"])
                                (
                                    angle_within_range,
                                    angle_message,
                                    face_angles,
                                    difference,
                                ) = self.is_face_normal(
                                    task_data["rvec"], task_data["tvec"]
                                )
                                x_offset, y_offset = self.get_target_location(
                                    difference
                                )
                                self.current_content.update_target_location(
                                    x_offset, y_offset
                                )
                                if is_face_center:
                                    if not angle_within_range:
                                        if (
                                            not self.current_content.head_frontal_container.alert_label.visible
                                        ):
                                            self.current_content.head_frontal_container.show_alert(
                                                angle_message
                                            )
                                        self.calibration_ok_time = None
                                        self.current_content.target_ring.data = 0
                                    if angle_within_range:
                                        if (
                                            not self.current_content.head_frontal_container.ok_label.visible
                                        ):
                                            self.current_content.head_frontal_container.show_ok()
                                        if self.calibration_ok_time is None:
                                            self.calibration_ok_time = time.time()
                                        else:
                                            elapsed_time = (
                                                time.time() - self.calibration_ok_time
                                            )
                                            self.current_content.target_ring.data = (
                                                elapsed_time
                                            )
                                            if (
                                                elapsed_time
                                                >= self.calibration_ok_count
                                            ):
                                                (
                                                    landmarks_2d,
                                                    landmarks_3d,
                                                ) = self.get_2d_3d_landmarks(
                                                    task_data["face_landmarks"]
                                                )
                                                self.current_content.hide_ok_container()
                                                self.current_content.head_frontal_container.show_ok()
                                                self.setup_iris_calibration_thread = threading.Thread(
                                                    target=self.setup_iris_calibration_func,
                                                    args=(landmarks_2d,
                                                          landmarks_3d),
                                                    daemon=True,
                                                )
                                                self.setup_iris_calibration_thread.start()
                                                self.switching_pipeline = True
                                                self.current_content.show_calibration_loader()
                                else:
                                    self.current_content.head_frontal_container.show_alert(
                                        message
                                    )
                            else:
                                if self.face_missing_start_time is None:
                                    self.face_missing_start_time = time.time()
                                else:
                                    if (
                                        time.time() - self.face_missing_start_time
                                    ) > self.cancel_time:
                                        self.module.session.logger.info(
                                            "Face is missing for more than"
                                            + str(self.cancel_time)
                                            + "seconds",
                                        )
                                        self.module.session.logger.info(
                                            self.name + " task cancelled"
                                        )
                                        self.module.session.on_back_to_menu()
                else:
                    if not self.start_head_pose_count_down:
                        self.start_head_pose_count_down = True
                        self.start_time = time.time()
                        self.current_content.initalized_iris_target_gifs()
                    else:
                        elapsed_time = time.time() - self.start_time
                        if elapsed_time >= self.head_pose_calibration_time:
                            self.head_pose_calibrated = True
                            self.start_head_pose_count_down = False
                            self.current_content.switch_to_iris_calibration()
            else:
                if (
                    self.current_content.instruction_text.data
                    != messages["move_head_hint_2"]
                ):
                    self.current_content.instruction_text.data = messages[
                        "move_head_hint_2"
                    ]
                if self.current_iris_target is None:
                    self.calibration_location_index += 1
                    if self.calibration_location_index < len(
                        self.calibration_locations
                    ):
                        self.current_iris_target = self.calibration_locations[
                            self.calibration_location_index
                        ]
                        self.current_iris_target_name = "calibration_video_" + str(
                            self.calibration_location_index
                        )
                        self.current_content.show_iris_target(
                            self.calibration_locations,
                            self.calibration_location_index,
                        )
                        oakd_capture({"reset_video": False,
                                      "video_file_name": ""},
                                     service_states={"action": "open_new_video",
                                                     "open_new_video": self.current_iris_target_name}).get_data()
                        self.previous_left_eye_x = 0
                        self.previous_left_eye_y = 0
                        self.previous_right_eye_x = 0
                        self.previous_right_eye_y = 0
                        self.target_appear_time = time.time()

                if self.calibration_location_index >= len(self.calibration_locations):
                    filename = (
                        self.temp_collected_data_path + "/calibration_video_meta.json"
                    )
                    os.makedirs(
                        os.path.dirname(self.temp_collected_data_path), exist_ok=True
                    )
                    with open(
                        filename,
                        "w",
                    ) as f:
                        f.write(json.dumps(self.video_meta))
                    self.module.session.logger.info(
                        self.name + " task: saved calibration_video_meta.json"
                    )
                    self.on_task_complete(
                        "calibration", self.iris_location_on_target)
                else:
                    frame_data = oakd_capture({"reset_video": False,
                                               "video_file_name": self.current_iris_target_name},
                                              service_states={"action": "capture",
                                                              "save_path": self.temp_collected_data_path})
                    task_data = None
                    if frame_data:
                        head_pose_result = head_pose_estimation(
                            {"camera_input": frame_data})
                        if head_pose_result is not None and not isinstance(head_pose_result, ExceptionTypes):
                            task_data = head_pose_result.get_data()

                    if task_data:
                        if "frame" in task_data:
                            self.current_content.head_pose_image.set_data(
                                task_data["frame"])
                        if len(task_data["face_location"]) > 0:
                            is_face_center, message, num_face = self.check_face_center(
                                task_data["face_location"]
                            )
                            if self.face_missing_start_time is not None:
                                self.face_missing_start_time = None
                            (
                                angle_within_range,
                                angle_message,
                                face_angles,
                                difference,
                            ) = self.is_face_normal(
                                task_data["rvec"],
                                task_data["tvec"],
                            )
                            x_offset, y_offset = self.get_target_location(
                                difference)
                            self.current_content.update_target_location(
                                x_offset, y_offset
                            )
                            continue_countdown = False
                            if is_face_center:
                                if not angle_within_range:
                                    if (
                                        not self.current_content.calibration_alert_container.visible
                                    ):
                                        self.current_content.show_alert()
                                        self.current_content.head_frontal_container.show_alert(
                                            angle_message
                                        )
                                if angle_within_range:
                                    if (
                                        self.current_content.calibration_alert_container.visible
                                    ):
                                        self.current_content.hide_alert()
                                        self.current_content.head_frontal_container.show_ok()
                                    if (
                                        time.time() - self.target_appear_time
                                        >= self.params[
                                            "calibration_start_countdown_time"
                                        ]
                                    ):
                                        self.iris_focused_on_target = True
                                    else:
                                        self.iris_focused_on_target = False

                                    if self.iris_focused_on_target:
                                        continue_countdown = True

                                        if self.target_start_time is None:
                                            self.target_start_time = time.time()
                                        else:
                                            elapsed_time = (
                                                time.time() - self.target_start_time
                                            )
                                            self.current_content.current_iris_target.time_meter.data = (
                                                elapsed_time
                                            )
                                            if (
                                                elapsed_time
                                                >= self.iris_calibration_time
                                            ):
                                                self.current_content.current_iris_target.time_meter.data = (
                                                    self.iris_calibration_time
                                                )
                                                self.module.session.logger.info(
                                                    self.name
                                                    + " task: checking if data is recorded..."
                                                )
                                                if not os.path.isfile(
                                                    self.temp_collected_data_path
                                                    + self.current_iris_target_name
                                                    + ".h264"
                                                ):
                                                    self.module.session.logger.error(
                                                        "Video File: "
                                                        + self.current_iris_target_name
                                                        + " for task "
                                                        + self.name
                                                        + " is not found, raising exception"
                                                    )
                                                    self.module.session.logger.error(
                                                        self.name
                                                        + " task recorded data: "
                                                        + self.current_iris_target_name
                                                        + " not found"
                                                    )
                                                    sys.stderr.write(
                                                        json.dumps(
                                                            {
                                                                "app_exception_msg": self.name
                                                                + ": "
                                                                + messages[
                                                                    "recordedDataNotFound"
                                                                ]
                                                            }
                                                        )
                                                        + "\n"
                                                    )
                                                    self.exception_retry_attempt += 1
                                                    if (self.exception_retry_attempt > 2):
                                                        raise Exception(
                                                            messages["recordedDataNotFound"]
                                                        )
                                                    else:
                                                        self.module.session.logger.info(
                                                            self.name
                                                            + " task recorded data: "
                                                            + self.current_iris_target_name
                                                            + " not found, retrying..."
                                                        )
                                                        self.calibration_location_index -= 1
                                                else:
                                                    self.exception_retry_attempt = 0
                                                    self.module.session.logger.info(
                                                        self.name
                                                        + " task recorded data: "
                                                        + self.current_iris_target_name
                                                        + " data found..."
                                                    )
                                                self.current_iris_target = None
                                                self.iris_focused_on_target = False
                                                self.target_start_time = None
                            else:
                                if (
                                    not self.current_content.calibration_alert_container.visible
                                ):
                                    self.current_content.show_alert()
                                    self.current_content.head_frontal_container.show_alert(
                                        message
                                    )
                                    self.target_appear_time = time.time()
                            if not continue_countdown:
                                self.target_start_time = None
                                self.current_content.current_iris_target.time_meter.data = (
                                    0
                                )
                                if self.target_start_time is not None:
                                    oakd_capture(
                                        {"reset_video": True,
                                         "video_file_name": ""}).get_data()
                        else:
                            if self.face_missing_start_time is None:
                                self.face_missing_start_time = time.time()
                            else:
                                if (
                                    time.time() - self.face_missing_start_time
                                ) > self.cancel_time:
                                    self.module.session.logger.info(
                                        "Face is missing for more than"
                                        + str(self.cancel_time)
                                        + "seconds",
                                    )
                                    self.module.session.logger.info(
                                        self.name + " task cancelled"
                                    )
                                    self.module.session.on_back_to_menu()
