from base_task import BaseTask
from screens.common_screen_element import TaskTitle
from screens.hand_movement.up_down_collection_screen import UpDownCollectionScreen
import os
import json
from service_registry import *
import sys
import time
from pathlib import Path
from task_data import messages
from cortic_platform.sdk.app_events import ExceptionTypes

class UpDownCollectionTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("up_down_collection", module, on_task_complete, task_params)
        with open(
            os.path.dirname(os.path.realpath(__file__)) + "/parameters.json"
        ) as f:
            self.params = json.load(f)
        self.current_hand = task_params
        self.temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/temp_collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/"
            + self.current_hand
            + "_up_down_collection/"
        )
        self.collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/"
            + self.current_hand
            + "_up_down_collection/"
        )
        Path(self.temp_collected_data_path).mkdir(parents=True, exist_ok=True)
        self.module.session.logger.info(
            "Created temp data collection dir for "
            + self.current_hand
            + " hand flip task"
        )
        # os.makedirs(os.path.dirname(self.collected_data_path), exist_ok=True)
        self.title = TaskTitle(
            [840, 32, 571, 42], self.module.name, self.module.icon)
        self.screens = [UpDownCollectionScreen([0, 100, 1920, 918])]
        self.rotate_once = False
        self.num_rotates = 0
        self.num_seconds = 0
        self.current_palmer = False
        self.front_once = False
        self.back_once = False
        self.start_time = None
        self.reset = False
        self.target_taps = 15
        self.target_seconds = 20
        oakd_capture({"reset_video": True, "video_file_name": ""}).get_data()
        self.module.session.logger.info(
            "("
            + self.module.session.patient_id
            + ") "
            + self.name
            + " task initialized"
        )

    def get_thumb_pinky_distance(self, hand_infos):
        if len(hand_infos) == 0:
            return -1
        hand_info = None
        for hand in hand_infos:
            if hand["label"] == self.current_hand:
                hand_info = hand
        if hand_info is None:
            return -1
        return hand_info["thumb_pinky_distance"]

    def count_rotate(self, hand_infos, finger_distance):
        if len(hand_infos) == 0:
            return
        hand_info = None
        for hand in hand_infos:
            if hand["label"] == self.current_hand:
                hand_info = hand
        if hand_info is None:
            return
        palmar = hand_info["palmar"]
        if palmar:
            if not self.front_once:
                if not self.reset:
                    self.front_once = True
        else:
            if not self.back_once:
                self.back_once = True
                self.reset = False
        if self.front_once and self.back_once:
            self.num_rotates += 1
            self.current_content.stat_container.taps_meter.data = self.num_rotates
            self.front_once = False
            self.back_once = False
            self.reset = True

    def are_both_palmar(self, hand_infos):
        both_palmar = True
        for hand in hand_infos:
            if not hand["palmar"]:
                both_palmar = False
        return both_palmar

    def is_palmar(self, hand_infos):
        palmer = True
        if len(hand_infos) == 0:
            return palmer
        hand_info = None
        for hand in hand_infos:
            if hand["label"] == self.current_hand:
                hand_info = hand
        if hand_info is None:
            return palmer
        return hand_info["palmar"]

    def are_both_dorsal(self, hand_infos):
        both_dorsal = True
        for hand in hand_infos:
            if hand["palmar"]:
                both_dorsal = False
        return both_dorsal

    def process(self):
        if isinstance(self.current_content, UpDownCollectionScreen):
            if self.num_rotates >= self.target_taps or self.num_seconds >= self.target_seconds:
                oakd_capture({"reset_video": True, "video_file_name": ""})
                self.module.session.logger.info(
                    self.name + " task complete, checking if data is recorded..."
                )
                if not os.path.isfile(
                    self.temp_collected_data_path
                    + self.current_hand
                    + "_up_down"
                    + ".h264"
                ):
                    self.module.session.logger.error(
                        "Video File for task "
                        + self.name
                        + " is not found, raising exception"
                    )
                    self.module.session.logger.error(
                        self.name + " task recorded data not found"
                    )
                    sys.stderr.write(
                        json.dumps(
                            {
                                "app_exception_msg": self.name
                                + ": "
                                + messages["recordedDataNotFound"]
                            }
                        )
                        + "\n"
                    )
                    raise Exception(messages["recordedDataNotFound"])
                else:
                    self.module.session.logger.info(
                        self.name + " task recorderd data found..."
                    )
                self.on_task_complete(
                    self.current_hand + "_up_down_collection")
            else:
                frame_data = oakd_capture({"reset_video": False, "video_file_name": self.current_hand + "_up_down"},
                                          service_states={"action": "capture",
                                                          "save_path": self.temp_collected_data_path})
                task_data = None
                if frame_data is not None:
                    finger_tapping_data = finger_tapping_detector(
                        {"camera_input": frame_data})
                    if finger_tapping_data is not None and not isinstance(finger_tapping_data, ExceptionTypes):
                        task_data = finger_tapping_data.get_data()
                if task_data:
                    self.current_content.main_camera_image.data = task_data["frame"]
                    thumb_pinky_distance = self.get_thumb_pinky_distance(
                        task_data["hand_infos"]
                    )
                    if not self.rotate_once:
                        if thumb_pinky_distance != -1:
                            if not self.is_palmar(task_data["hand_infos"]):
                                self.rotate_once = True
                                self.current_content.start_statistic()
                                self.start_time = time.time()
                    else:
                        elapsed_time = time.time() - self.start_time
                        if elapsed_time >= 1:
                            self.num_seconds += 1
                            self.start_time = time.time()
                            elapsed_time = 0
                        self.current_content.stat_container.time_meter.data = (
                            self.num_seconds + elapsed_time
                        )
                        if thumb_pinky_distance != -1:
                            self.count_rotate(
                                task_data["hand_infos"], thumb_pinky_distance
                            )
