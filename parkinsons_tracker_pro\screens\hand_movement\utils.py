import os
import base64
import cv2
import numpy as np


def read_asset_image(filename):
    with open(
        os.path.dirname(os.path.realpath(__file__)) +
        "/../../assets/" + filename, "rb"
    ) as image_file:
        image = base64.b64encode(image_file.read()).decode("ascii")
    return image


def deserialize_frame(frame_data):
    jpg_original = base64.b64decode(frame_data)
    jpg_as_np = np.frombuffer(jpg_original, dtype=np.uint8)
    frame = cv2.imdecode(jpg_as_np, flags=1)
    return frame
