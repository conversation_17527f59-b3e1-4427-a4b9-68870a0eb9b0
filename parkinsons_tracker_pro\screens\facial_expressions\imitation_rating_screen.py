from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image
from cortic_platform.sdk.ui.input_widgets import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, RadioButtonList
from utils import read_asset_image
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)) + "/../..")
from screens.task_background_screen import TaskBackgroundContainer
from screens.common_screen_element import TaskTitle
from task_data import messages


class ImitationRatingScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1520, 900],
        image_url="https://fastly.picsum.photos/id/191/200/300.jpg?hmac=CHbfFOcICYpJ4GXstpLztK5ds_l5NYOdgHORuCEIY_g",
        instruction="Instruction Text",
        expression_type="expression_type",
        subtitle="Emotional Recognition and Facial Mimicry",
        text_height=80,
        font_size=30,
        image_width=560,
        image_height=700,
        radius=0,
        border_color=None,
        on_continue=None,
        on_redo_previous=None,
    ):
        super().__init__([0, 0, 1920, 1080])
        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name="Facial Expression",
                                                           task_icon=read_asset_image("facial.png")))
        
        self.content = Container([200, 90, 1520, 900])
        self.content.border_color = border_color
        self.content.corner_radius = radius
        self.content.alpha = 0
    
        self.on_redo_previous = on_redo_previous
        self.emotion_labels = [
            "happiness",
            "sadness",
            "disgust",
            "fear",
            "surprise",
            "anger",
        ]
        self.alpha = 0
        self.expression_type = expression_type

        self.subtitle = Label(
            [0, 15, 1520, 60],
            data=subtitle
        )
        self.subtitle.font_size = 40
        self.subtitle.alignment = "center"
        self.subtitle.font_weight = "bold"

        self.instruction_text = Label(
            [0, 70, 1520, text_height],
            data=instruction
        )
        self.instruction_text.font_size = font_size
        self.instruction_text.alignment = "center"
        self.instruction_text.font_weight = "thin"

        self.display_image = Image(
            [20, text_height + 80 + 10, image_width, image_height],
            data=image_url
        )

        self.display_image.use_url = True
        self.display_image.alpha = 1
        self.display_image.corner_radius = 10
        self.display_image.border_color = "#000000"

        self.emotion_hint = Label(
            [750, text_height + 80 + 10, 700, 40],
            data=messages["emotion_rate_hint"]
        )
        self.emotion_hint.font_size = 28
        self.emotion_hint.alignment = "left"

        self.emotions = RadioButtonList(
            [860, text_height + 80 + 10 + 60, 400, 400],
            items=self.emotion_labels
        )
        self.emotions.label_font_size = 28
        self.emotions.item_height = 60
        self.emotions.label_left_margin = 10
        self.emotions.button_scale = 1.5

        self.emotion_intense_hint = Label(
            [750, text_height + 80 + 10 + 450, 700, 40],
            data=messages["emotion_intense_hint"]
        )
        self.emotion_intense_hint.font_size = 28
        self.emotion_intense_hint.alignment = "left"

        self.emotion_intensity = Slider(
            [700, text_height + 80 + 10 + 520, 700, 60]
        )
        self.emotion_intensity.min_value = 0
        self.emotion_intensity.max_value = 10
        self.emotion_intensity.intervals = 10

        self.emotion_intensity_zero_label = Label(
            [721, text_height + 80 + 10 + 520 + 30, 75, 55],
            data="0"
        )
        self.emotion_intensity_zero_label.font_size = 22.5
        self.emotion_intensity_zero_label.alignment = "center"

        self.emotion_intensity_zero_label_2 = Label(
            [721, text_height + 80 + 10 + 520 + 55, 75, 55],
            data="(None)"
        )
        self.emotion_intensity_zero_label_2.font_size = 22.5
        self.emotion_intensity_zero_label_2.alignment = "center"

        self.emotion_intensity_strongest_label = Label(
            [1280, text_height + 80 + 10 + 520 + 30, 130, 55],
            data="10"
        )
        self.emotion_intensity_strongest_label.font_size = 22.5
        self.emotion_intensity_strongest_label.alignment = "center"

        self.emotion_intensity_strongest_label_2 = Label(
            [1280, text_height + 80 + 10 + 520 + 55, 130, 55],
            data="(Strongest)"
        )
        self.emotion_intensity_strongest_label_2.font_size = 22.5
        self.emotion_intensity_strongest_label_2.alignment = "center"

        self.continue_button = Button(
            [700 + 350 - 100, text_height + 80 + 10 + image_height - 64, 200, 64]
        )
        self.continue_button.label = messages["next"]
        self.continue_button.label_font_size = 22
        self.continue_button.on_widget_event = on_continue

        self.redo_previous_button = Button(
            [30, 924, 160.67, 64]
        )
        self.redo_previous_button.label = "Redo Previous"
        self.redo_previous_button.button_color = "#FFC700"
        self.redo_previous_button.label_font_color = "#000000"
        self.redo_previous_button.label_font_size = 20
        self.redo_previous_button.on_widget_event = self.on_redo_previous

        self.content.add_children(
            [
                self.subtitle,
                self.instruction_text,
                self.display_image,
                self.emotion_hint,
                self.emotions,
                self.emotion_intense_hint,
                self.emotion_intensity,
                self.emotion_intensity_zero_label,
                self.emotion_intensity_zero_label_2,
                self.emotion_intensity_strongest_label,
                self.emotion_intensity_strongest_label_2,
                self.continue_button,
            ]
        )

        self.task_background.update_content(self.content)
        self.task_background.add_child(self.redo_previous_button)
        self.add_child(self.task_background)