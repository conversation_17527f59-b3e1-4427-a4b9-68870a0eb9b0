from cortic_platform.sdk.ui.basic_widgets import Container, Label
from cortic_platform.sdk.ui.input_widgets import Button
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from common_utils import read_asset_image
from task_background_screen import TaskBackgroundContainer
from common_screen_element import TaskTitle

class TextInstructionScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1520, 950],
        instruction_text="Instruction Text",
        subtitle=None,
        text_width=1520,
        text_height=850,
        font_size=48,
        radius=0,
        border_color=None,
        on_continue=None,
    ):
        super().__init__([0, 0, 1920, 1080])

        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name="Hand Movement",
                                                           task_icon=read_asset_image("hand.png")))
        
        self.content = Container([200, 90, 1520, 950])
        self.content.border_color = border_color
        self.content.corner_radius = radius
        self.content.alpha = 0

        self.instruction_text = Label(
            [0, 0, text_width, text_height],
            data=instruction_text
        )
        self.instruction_text.alignment = "center"
        self.instruction_text.font_size = font_size

        self.continue_button = Button(
            [690, 770, 140, 64]
        )
        self.continue_button.label = "Start"
        self.continue_button.font_size = 22
        self.continue_button.on_widget_event = on_continue

        if subtitle is not None:
            self.subtitle = Label(
                [0, 15, text_width, 80],
                data=subtitle
            )
            self.subtitle.alignment = "center"
            self.subtitle.font_size = 50
            self.subtitle.font_weight = "bold"

            self.content.add_child(self.subtitle)

        self.content.add_children([self.instruction_text, self.continue_button])

        self.task_background.update_content(self.content)
        self.add_child(self.task_background)
