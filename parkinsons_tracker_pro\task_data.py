instructions = {
    "finger_calibration": "Tap the index finger on the thumb of each hand as **QUICKLY** and as **BIG** as possible",
    "okn": [
        "This module aims to track your eye movements during multiple tasks.\n\n While you follow the instructions of each task, plsease try to keep your head as still as possible.",
        "Watch the strip as it goes by.",
        "Take some rest and whenever you're ready for the next task, press start",
        "Take some rest and whenever you're ready for the next task, press start",
        "Take some rest and whenever you're ready for the next task, press start",
        "Take a break until you are ready for the next task. Whenever you're ready, press continue button.",
    ],
    "eye_calibration": "Please enter full screen mode before continue...",
    "hand_calibration_open_close": "Open and close each hand as **FULLY** and as **QUICKLY** as possible",
    "hand_calibration_up_down": "Rotate each hand as **QUICKLY** and as **BIG** as possible",
    "both_calibration_still": "Hold your hands in front of face with your palms facing towards you",
    "neutral_facial_expression": "On the next page, keep a **neutral face** for 10 seconds",
    "raising_eyebrows": "In this task, please raise your eyebrows as high as possible and keep it like that for at least 10 seconds, like the example below. \\\n\\\n(On the next page, you will be given the opportunity to record your facial expression.)",
    "closing_eyes": "In this task, please close your eyes as tight as possible and keep them closed for at least 10 seconds, like the example below. \\\n\\\n(On the next page, you will be given the opportunity to record your facial expression.)",
    "big_smile": "In this task, please show as many teeth as you can with a big smile and maintain for at least 10 seconds, like the example below. \\\n\\\n(On the next page, you will be given the opportunity to record your facial expression.)",
    "facial_expression_during_speech": 'Whenever you are ready, press the "Start Recording" button and describe what is going on in the picture below casually with 1-2 sentences. You will be given 10 seconds.',
    "facial_expression_text": {
        "happy": "Recall your happiest memory and make a **happy** face",
        "sad": "Recall a sad memory and make a **sad** face",
        "angry": "Imagine an angry situation and make an **angry** face",
        "disgust": "Imagine a disgusting situation and make a **disgusted** face"
    },
    "emotional_recognition_facial_mimicry_description": "In this task, we will show you some pictures with various facial expressions. We'll ask you to both assess the facial expression and then imitate it by yourself.",
    "emotional_recognition_facial_mimicry_rating": "Please look at the photo on the left and answer the questions on the right.",
    "emotional_recognition_facial_mimicry_recording": "Now, please mimic the expression for a few seconds."
}

instruction_videos = {
    "finger_tapping": "Finger_Tapping_Instruction.mp4",
    "okn": [
        {
            "filename": "OKN_Video1.mp4",
            "target_velocity": 573,
        },
        {
            "filename": "OKN_Video2.mp4",
            "target_velocity": 640,
        },
        {
            "filename": "OKN_Video3.mp4",
            "target_velocity": 654,
        },
        {
            "filename": "OKN_Video4.mp4",
            "target_velocity": 642,
        },
    ],
    "hand_open_close": "Hand Open and Close.mp4",
    "hand_flip": "Hand Flip.mp4",
    "hand_still": "Hand Hold Still.mp4",
    "basic_facial": [
        "Facial Expression Basics_Neutral.mp4",
        "Facial Expression Basics_Raise_Eyebrows.mp4",
        "Facial Expression Basics_Eyeclosure.mp4",
        "Facial Expression Basics_ShowTeeth.mp4",
        "Cookie_Theft_Color.png",
    ],
}

messages = {
    "ok": "OK",
    "no_face": "No face detected",
    "face_visible": "Face Visible",
    "face_not_visible": "Face not visible",
    "hand_not_visible": "Hand not visible",
    "hands_not_visible": "Hands not visible",
    "wrong_hand": "Wrong hand",
    "right_hand_not_visible": "Right hand not visible",
    "left_hand_not_visible": "Left hand not visible",
    "not_fully_visible": "Not fully visible",
    "rotate_left": "Rotate your face to left",
    "rotate_right": "Rotate your face to right",
    "rotate_up": "Rotate your face up",
    "rotate_down": "Rotate your face down",
    "rotate_clockwise": "Rotate your in clockwise direction",
    "rotate_cclockwise": "Rotate your in counter-clockwise direction",
    "move_left": "Move to your left",
    "move_right": "Move to your right",
    "move_lower": "Move lower",
    "move_upper": "Move upper",
    "move_closer": "Move closer",
    "move_head_hint": "Please move your head so that the red cursor goes into the gray circle, and then keep looking at it until the circle turns green",
    "move_head_hint_2": "Please ensure the red cursor is within the green circle while looking at different targets on the screen",
    "iris_target_hint": "While holding your head still, please focus on the animated target on the screen",
    "hand_needs_visible": "Make sure your hand is visible",
    "hands_need_visible": "Make sure both hands are visible",
    "face_hand_hint": "Position your hands so that the camera has a clear view",
    "face_left_hand_hint": "Hold your left hand still for a few seconds",
    "face_right_hand_hint": "Hold your right hand still for a few seconds",
    "face_hint": "Position your face so that the camera has a clear view",
    "left_hand_calib_title": "Left Hand Positioning",
    "right_hand_calib_title": "Right Hand Positioning",
    "both_hand_calib_title": "Positioning of both hands",
    "face_calib_title": "Face Positioning",
    "hand_visible": " hand visible",
    "both_hand_visible": "both hands visible",
    "movements": "Movements",
    "time": "Time",
    "hand_back_ready": "Face your hands backward when ready",
    "alternate_hand_hint": "Flip your hand at least 15 times",
    "close_hand_ready": "Open and close your fist",
    "open_close_hint": "Open and close your hand at least 15 times",
    "lay_hand_ready": "Lay down your hands when ready",
    "keep_hand_flat_hint": "Hold your hands still",
    "taps": "Taps",
    "speed": "Speed",
    "halt": "Halt",
    "hestitation": "Hestitation",
    "amplitude": "Amplitude",
    "finger_distance_unit": "cm",
    "decr_amplitude": "Decrementing Amplitude",
    "start_tap_ready": "Start tapping when ready",
    "tap_finger_hint": "Tap your fingers at least 15 times",
    "complete": "Complete",
    "skip_right_hand": "Skip Right Hand",
    "redo_left_hand": "Redo Left Hand",
    "emotion_rate_hint": "Which emotion best describes the face shown?",
    "emotion_intense_hint": "How intense do you think the emotion is?",
    "start_imitation": "Start Imitation",
    "press_start_hint": 'Press the "Start Recording" Button when you are ready.',
    "next": "Next",
    "enterPatientPIN": "Please enter the 5 digit PIN",
    "recordedDataNotFound": "Recorded data is not found, this can be caused by a system issue, please reboot the computer and retry this moduel",
    "neutralFace": "Make **Neutral** Face",
    "mimicFace": "**Mimic** Face",
    "facial_expression_face": {
        "happy": "Make **Happy** Face",
        "sad": "Make **Sad** Face",
        "angry": "Make **Angry** Face",
        "disgust": "Make **Disgusted** Face",
    },
}
