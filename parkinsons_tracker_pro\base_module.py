import requests


class BaseModule:
    def __init__(self, name, icon, background, on_module_complete=None):
        self.session = None
        self.patient_name = ""
        self.patient_id = ""
        self.access_token = ""
        self.base_url = "https://yuq8df6a4g.execute-api.ca-central-1.amazonaws.com/"
        self.name = name
        self.icon = icon
        self.background = background
        self.on_module_complete = on_module_complete
        self.task_list = []
        self.resources = {}
        self.current_task = None
        self.current_task_idx = -1

    def start(self, task_params=None):
        if len(self.task_list) > 0:
            self.current_task = self.task_list[0](
                self, self.on_task_complete, task_params=task_params
            )
            self.current_task_idx = 0
            self.current_task.initalize_screen()
            try:
                self.current_task.current_content.task_background.header_container.menu_button.on_widget_event = self.session.on_back_to_menu
                self.current_task.current_content.task_background.header_container.patient_name = self.patient_name
                self.current_task.current_content.task_background.header_container.time_string = self.session.session_time
            except:
                pass
            self.session.update_current_task_screen(self.current_task.current_content)
            # self.background.update_content(self.current_task.current_content)
            self.current_task.start()
        else:
            self.on_module_complete()

    def next_task(self, task_params=None):
        self.current_task.stop()
        self.current_task = self.task_list[self.current_task_idx + 1](
            self, self.on_task_complete, task_params=task_params
        )
        self.current_task_idx += 1
        self.current_task.initalize_screen()
        try:
            self.current_task.current_content.task_background.header_container.menu_button.on_widget_event = self.session.on_back_to_menu
            self.current_task.current_content.task_background.header_container.patient_name = self.patient_name
            self.current_task.current_content.task_background.header_container.time_string = self.session.session_time
        except:
            pass
        self.session.update_current_task_screen(self.current_task.current_content)
        # self.background.header_container.update_title(self.current_task.title)
        # self.background.update_content(self.current_task.current_content)
        self.current_task.start()

    def redo_task(self, task_index, task_params=None):
        self.current_task.stop()
        self.current_task_idx = task_index
        self.current_task = self.task_list[self.current_task_idx](
            self, self.on_task_complete, task_params=task_params
        )
        self.current_task.initalize_screen()
        try:
            self.current_task.current_content.task_background.header_container.menu_button.on_widget_event = self.session.on_back_to_menu
            self.current_task.current_content.task_background.header_container.patient_name = self.patient_name
            self.current_task.current_content.task_background.header_container.time_string = self.session.session_time
        except:
            pass
        self.session.update_current_task_screen(self.current_task.current_content)
        self.current_task.start()

    def on_task_complete(self, data=None):
        pass
        # self.uploader.scan_file_for_upload()

    def on_module_start(self):
        pass

    def init_resources(self):
        pass

    def request_module_resouces(self, resource_names):
        headers = {
            "Authorization": "Bearer " + self.access_token,
            "Content-Type": "application/json",
        }
        download_api = self.base_url + "subject/download"
        for name in resource_names:
            self.resources[name] = ""
            payload = {
                "module_name": self.name.lower().replace(" ", "_"),
                "file_name": name,
            }
            # print(download_api, payload)
            response = requests.get(download_api, headers=headers, params=payload)
            if response.ok:
                self.resources[name] = response.json()["url"]
            else:
                print(response.json())

    def stop(self):
        if self.current_task_idx != -1:
            self.current_task.stop()
            self.current_task = None
            self.current_task_idx = -1
