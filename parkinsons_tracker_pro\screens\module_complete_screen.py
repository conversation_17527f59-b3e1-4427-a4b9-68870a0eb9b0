from cortic_platform.sdk.ui.basic_widgets import Container, Label
from cortic_platform.sdk.ui.input_widgets import Button
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from common_utils import read_asset_image
from task_background_screen import TaskBackgroundContainer
from common_screen_element import TaskTitle

class ModuleCompleteScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1520, 950],
        instruction_text="Module Completed",
        task_name="Task",
        asset_image=read_asset_image("hand.png"),
        subtitle=None,
        text_width=1520,
        text_height=850,
        font_size=48,
        radius=0,
        border_color=None,
        on_module_complete=None,
        on_redo_previous=None
    ):
        super().__init__([0, 0, 1920, 1080])

        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name=task_name,
                                                           task_icon=asset_image))
        
        self.content = Container([200, 90, 1520, 950])
        self.content.border_color = border_color
        self.content.corner_radius = radius
        self.content.alpha = 0

        self.instruction_text = Label(
            [0, 0, text_width, text_height],
            data=instruction_text
        )
        self.instruction_text.alignment = "center"
        self.instruction_text.font_size = font_size

        self.continue_button = Button(
            [640, 700, 240, 64]
        )
        self.continue_button.label = "Complete"
        self.continue_button.font_size = 22
        self.continue_button.on_widget_event = on_module_complete

        if subtitle is not None:
            self.subtitle = Label(
                [0, 15, text_width, 80],
                data=subtitle
            )
            self.subtitle.alignment = "center"
            self.subtitle.font_size = 50
            self.subtitle.font_weight = "bold"

            self.content.add_child(self.subtitle)

        self.redo_button = Button(
            [30, 924, 200.67, 64]
        )
        self.redo_button.label = "Redo Previous Task" #messages["redo_left_hand"]
        self.redo_button.button_color = "#FFC700"
        self.redo_button.label_font_color = "#000000"
        self.redo_button.label_font_size = 20
        self.redo_button.on_widget_event = on_redo_previous

        self.content.add_children([self.instruction_text, self.continue_button])

        self.task_background.update_content(self.content)
        self.add_children([self.task_background, self.redo_button])
