from base_module import BaseModule
from tasks.hand_movement.left_calibration_open_close_task import (
    LeftCalibrationOpenCloseTask,
)
from tasks.hand_movement.right_calibration_open_close_task import (
    RightCalibrationOpenCloseTask,
)
from tasks.hand_movement.both_calibration_up_down_task import BothCalibrationUpDownTask
from tasks.hand_movement.right_calibration_up_down_task import (
    RightCalibrationUpDownTask,
)
from tasks.hand_movement.left_calibration_up_down_task import LeftCalibrationUpDownTask
from tasks.hand_movement.both_calibration_still_task import BothCalibrationStillTask
from tasks.hand_movement.open_close_collection_task import OpenCloseCollectionTask
from tasks.hand_movement.up_down_collection_task import UpDownCollectionTask
from tasks.hand_movement.still_collection_task import StillCollectionTask

from service_registry import *
from utils import read_asset_image
from task_data import instruction_videos
import numpy as np


class HandMovementModule(BaseModule):
    def __init__(self, background, on_module_complete=None):
        super().__init__(
            "Hand Movement",
            read_asset_image("hand.png"),
            background,
            on_module_complete,
        )
        self.task_list = [
            RightCalibrationOpenCloseTask,
            OpenCloseCollectionTask,
            LeftCalibrationOpenCloseTask,
            OpenCloseCollectionTask,
            # BothCalibrationUpDownTask,
            RightCalibrationUpDownTask,
            UpDownCollectionTask,
            LeftCalibrationUpDownTask,
            UpDownCollectionTask,
            BothCalibrationStillTask,
            StillCollectionTask,
        ]

    def init_resources(self):
        self.session.loading_background.visible = True
        self.session.widget_tree.update(self.session.loading_background)
        self.request_module_resouces(
            [
                instruction_videos["hand_open_close"],
                instruction_videos["hand_flip"],
                instruction_videos["hand_still"],
            ]
        )
        self.session.app.reset_service_state("oakd_capture")
        self.session.app.reset_service_state("finger_tapping_detector")
        # Calling the two services to activate them under task mode.
        oakd_capture({"reset_video": True,
                      "video_file_name": ""}).get_data()
        finger_tapping_detector(
            {"camera_input": {"frame": np.zeros((720, 1280, 3), np.uint8)}}).get_data()
        self.session.loading_background.visible = False
        self.session.widget_tree.update(self.session.loading_background)

    def on_task_complete(self, data=None, redo=False):
        super().on_task_complete()
        if (redo):
            if data == "right_calibration_open_close":
                self.redo_task(task_index=0)
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "left_calibration_open_close":
                self.redo_task(task_index=2)
                self.current_task.current_content.task_background.header_container.show_timestamp()
            # elif data == "both_calibration_up_down":
            #     self.next_task(task_params="both")
            #     self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "right_calibration_up_down":
                self.redo_task(task_index=4)
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "left_calibration_up_down":
                self.redo_task(task_index=6)
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "both_calibration_still":
                self.redo_task(task_index=8)
                self.current_task.current_content.task_background.header_container.show_timestamp()
        else:
            if data == "right_calibration_open_close":
                self.next_task(task_params="right")
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "left_calibration_open_close":
                self.next_task(task_params="left")
                self.current_task.current_content.task_background.header_container.show_timestamp()
            # elif data == "both_calibration_up_down":
            #     self.next_task(task_params="both")
            #     self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "right_calibration_up_down":
                self.next_task(task_params="right")
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "left_calibration_up_down":
                self.next_task(task_params="left")
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "both_calibration_still":
                self.next_task(task_params="both")
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "right_open_close_collection":
                self.next_task(task_params="right")
                self.current_task.current_content.task_background.header_container.hide_timestamp()
            elif data == "left_open_close_collection":
                self.next_task(task_params="left")
                self.current_task.current_content.task_background.header_container.hide_timestamp()
            # elif data == "both_up_down_collection":
            #     self.next_task(task_params="both")
            #     self.current_task.current_content.task_background.header_container.hide_timestamp()
            elif data == "right_up_down_collection":
                self.next_task(task_params="right")
                self.current_task.current_content.task_background.header_container.hide_timestamp()
            elif data == "left_up_down_collection":
                self.next_task(task_params="left")
                self.current_task.current_content.task_background.header_container.hide_timestamp()
            elif data == "both_still_collection":
                self.current_task.current_content.task_background.header_container.hide_timestamp()
                self.on_module_complete()