from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image
from cortic_platform.sdk.ui.input_widgets import Button
from cortic_platform.sdk.ui.viewer_widgets import NetworkVideoPlayer
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from common_utils import read_asset_image
from task_background_screen import TaskBackgroundContainer
from common_screen_element import TaskTitle


class PlayerButton(Container):
    def __init__(self, rect=[0, 0, 100, 60], radius=0, border_color=None, on_event=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.capture_mouse_event = True
        self.on_pressed_event = on_event

        self.icon = Image([0, 0, 100, 59.665],
                          data=read_asset_image("play.png"))
        
        self.add_child(self.icon)


class VideoInstructionScreen(Container):
    def __init__(
        self,
        rect=[0, 0, 1520, 950],
        instruction_text="Instruction Text",
        instruction_video_url="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
        subtitle="Instruction Subtitle",
        text_width=1023,
        text_height=230,
        font_size=30,
        radius=0,
        border_color=None,
        centered_button=False,
        on_continue=None,
        task_name="Hand Movement",
        task_icon=read_asset_image("hand.png"),
        on_redo_previous=None
    ):
        super().__init__([0, 0, 1920, 1080])

        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name=task_name,
                                                           task_icon=task_icon))
        
        self.content = Container([200, 90, 1520, 950])
        self.content.border_color = border_color
        self.content.corner_radius = radius
        self.content.alpha = 0

        self.subtitle = Label(
            [248.5, 15, 1023, 60],
            data=subtitle,
        )
        self.subtitle.alignment = "center"
        self.subtitle.font_size = 40
        self.subtitle.font_weight = "bold"

        self.instruction_text = Label(
            [248.5, 70, text_width, text_height],
            data=instruction_text,
        )
        self.instruction_text.alignment = "left"
        self.instruction_text.font_size = font_size
        self.instruction_text.font_weight = "thin"

        self.instruction_video = NetworkVideoPlayer(
            [248.5, text_height + 70 + 10, 1023, 578], instruction_video_url
        )
        self.instruction_video.on_widget_event = self.on_video_stop

        self.continue_button = Button(
            [1360, 824, 140, 64]
        )
        self.continue_button.label = "Next"
        self.continue_button.font_size = 22
        self.continue_button.on_widget_event = on_continue

        self.player_button = Image(
            [248.5 + 1023 / 2 - 50, text_height +
                70 + 10 + 578 / 2 - 50, 100, 100],
            data=read_asset_image("play.png"),
        )
        self.player_button.visible = False
        self.player_button.capture_mouse_event = True
        self.player_button.on_widget_event = self.on_player_button_pressed

        self.content.add_children([self.subtitle, self.instruction_text,
                            self.instruction_video, self.player_button, self.continue_button])
        
        self.task_background.update_content(self.content)
        self.add_child(self.task_background)
        if (on_redo_previous is not None):
            self.redo_button = Button(
                [30, 924, 200.67, 64]
            )
            self.redo_button.label = "Redo Previous Task"
            self.redo_button.button_color = "#FFC700"
            self.redo_button.label_font_color = "#000000"
            self.redo_button.label_font_size = 20
            self.redo_button.on_widget_event = on_redo_previous
            self.add_child(self.redo_button)

    def on_video_stop(self, data):
        pass
        # if data == "stopped":
        #     self.instruction_video.data = ""
        #     self.player_button.visible = True
        #     self.root_widget_tree.update(self.player_button)

    def on_player_button_pressed(self, data):
        self.instruction_video.data = "play"
        self.player_button.visible = False
        self.root_widget_tree.update(self.player_button)
