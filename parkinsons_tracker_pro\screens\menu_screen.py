from cortic_platform.sdk.ui.basic_widgets import Container, Image, Label, Icon, HorizontalSeparator
from cortic_platform.sdk.ui.input_widgets import ListView, ListItem
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from main_background_screen import MainBackgroundContainer
from common_utils import read_asset_image
import math


class ModuleButton(Container):
    def __init__(
        self,
        rect=[0, 0, 250, 209],
        module_name="Module",
        module_picture=read_asset_image("finger.png"),
        radius=0,
        border_color=None,
        on_event=None,
    ):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.capture_mouse_event = True
        self.on_pressed_event = on_event
        self.on_widget_event = self.on_pressed
        self.background_color = "#F5F5F5"
        self.name = module_name
        self.picture = module_picture
        self.module_picture = Image([75, 32, 100, 100], data=self.picture)
        self.module_name = Label(
            [0, 148, rect[2], 32],
            data=self.name,
        )
        self.module_name.alignment = "center"
        self.module_name.font_weight = "bold"
        self.module_name.font_size = 24

        self.completed_icon = Image(
            [191, 11, 48, 48], data=read_asset_image("completed_icon.png")
        )
        self.completed_icon.visible = False

        self.add_children([self.module_picture, self.module_name, self.completed_icon])

    def on_pressed(self, data):
        if self.on_pressed_event is not None:
            self.on_pressed_event(self.name)

    def completed(self):
        self.completed_icon.visible = True
        for i in range(len(self.children)):
            if self.children[i]._id == self.completed_icon._id:
                self.children[i].visible = True
                break
        self.root_widget_tree.update(self)

    def incomplete(self):
        self.completed_icon.visible = False
        for i in range(len(self.children)):
            if self.children[i]._id == self.completed_icon._id:
                self.children[i].visible = False
                break
        self.root_widget_tree.update(self)


class CompleteSendButton(Container):
    def __init__(self, rect=[0, 0, 208, 48], radius=0, border_color=None, on_event=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.capture_mouse_event = True
        self.on_widget_event = on_event
        self.background_color = "#74AD2A"
        self.label = Label(
            [22, 11, 136, 26],
            data="Complete & Send"
        )
        self.label.font_size = 15
        self.label.font_color = "#ffffff"

        self.icon = Icon([146, 9, 24, 24], data="check")
        self.icon.icon_color = "#ffffff"

        self.add_children([self.label, self.icon])


class UploadButton(Container):
    def __init__(self, rect=[0, 0, 46, 48], radius=0, border_color=None, on_event=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.capture_mouse_event = True
        self.on_widget_event = on_event

        self.icon = Icon([10, 7, 24, 28], data="cloud_upload")
        self.icon.icon_color = "#5E7285"

        self.add_child(self.icon)


class CloseUploadStatusButton(Container):
    def __init__(self, rect=[0, 0, 30, 30], radius=0, border_color=None, on_event=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1
        self.capture_mouse_event = True
        self.on_widget_event = on_event

        self.icon = Icon([3, 2, 20, 20], data="x")
        self.icon.icon_color = "#5E7285"

        self.add_child(self.icon)


class UploadStatus(Container):
    def __init__(self, rect=[0, 0, 350, 450], radius=0, border_color=None, on_close=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1

        self.close_button = CloseUploadStatusButton(
            [305, 20, 30, 30], radius=8, border_color="#5E7285", on_event=on_close
        )

        self.title = Label(
            [20, 20, 250, 36], data="Upload Status"
        )
        self.title.font_size = 22
        self.title.font_weight = "bold"

        self.separator1 = HorizontalSeparator(
            [1, 70, 347, 4]
        )
        self.separator1.line_color = "#303133"
        self.separator1.thickness = 1

        self.uploading_label = Label(
            [20, 80, 220, 24],
            data="Currently Uploading ............",
        )
        self.uploading_label.font_size = 16
        self.uploading_label.font_weight = "bold"

        self.uploading_item = Label(
            [20, 110, 302, 55],
            data="",
        )
        self.uploading_item.font_size = 13
        self.uploading_item.italic = True

        self.elapsed_time = Label([240, 80, 102, 24], data="")
        self.elapsed_time.font_size = 13

        self.separator2 = HorizontalSeparator(
            [20, 170, 310, 4]
        )
        self.separator2.line_color = "#303133"
        self.separator2.thickness = 1

        self.waiting_label = Label(
            [20, 185, 300, 24],
            data="Waiting To Upload",
        )
        self.waiting_label.font_size = 16
        self.waiting_label.font_weight = "bold"

        self.waiting_items = ListView(
            [18, 215, 302, 180],
            items=[],
        )
        self.waiting_items.item_row_height = 60
        self.waiting_items.item_separation = 2
        self.waiting_items.item_selectable = False

        self.add_children(
            [
                self.close_button,
                self.title,
                self.separator1,
                self.uploading_label,
                self.elapsed_time,
                self.uploading_item,
                self.separator2,
                self.waiting_label,
                self.waiting_items,
            ]
        )

    def build_list_items(self, data):
        all_list_items = []
        for d in data:
            item = Label([0, 0, 300, 60], data=d)
            item.font_size = 13
            item.italic = True
            all_list_items.append(ListItem([item]))
        return all_list_items

    def build_update_list(self, data):
        update_data = []
        for d in data:
            item = Label([0, 0, 300, 60], data=d)
            item.font_size = 13
            item.italic = True
            list_item = ListItem([item])
            update_data.append(list_item)
        return update_data


class MenuScreen(Container):
    def __init__(
        self,
        radius=30,
        border_color=None,
        on_module_pressed=None,
        on_complete_btn_pressed=None,
        on_upload_btn_pressed=None,
    ):
        super().__init__([0, 0, 1920, 1080])
        self.background = MainBackgroundContainer([0, 0, 1920, 1080])
        
        # self.visibility_animation_duration = 1
        self.menu_widget = Container([278, 129.94, 1364, 820.13])

        self.menu_widget.corner_radius = radius
        self.menu_widget.border_color = border_color
        self.menu_widget.alpha = 1

        self.module_list = [
            {"name": "Finger Tapping", "icon": read_asset_image("finger.png"), "obj": None},
            {"name": "Hand Movement", "icon": read_asset_image("hand.png"), "obj": None},
            {"name": "Eye Movement", "icon": read_asset_image("eye.png"), "obj": None},
            {"name": "Facial Expression", "icon": read_asset_image("facial.png"), "obj": None},
        ]

        # self.on_event = on_complete_btn_pressed
        self.welcome_title = Label(
            [32, 43.06, 140, 38], data="Welcome ")
        self.welcome_title.font_size = 30

        self.patient_title = Label(
            [167.5, 43.06, 250, 38], data="John Doe"
        )
        self.patient_title.font_size = 30
        self.patient_title.font_weight = "bold"

        self.logo_image = Image(
            [1107.76, 32, 224.24, 58.13],
            data=read_asset_image("McKeown_logo_horizontal.png"),
        )

        self.separator1 = HorizontalSeparator(
            [32, 122.13, 1300, 4]
        )
        self.separator1.line_color = "#D9D9D9"
        self.separator1.thickness = 1
        
        self.choose_module_label = Label(
            [524.5, 156.13, 400, 38], data="Please choose a module"
        )
        self.choose_module_label.font_size = 30

        self.module_btns = []

        self.menu_widget.add_children(
            [
                self.welcome_title,
                self.patient_title,
                self.logo_image,
                self.separator1,
                self.choose_module_label,
            ]
        )

        self.button_margin = 32
        self.button_height = 209
        self.button_width = 250
        for i in range(len(self.module_list)):
            module = self.module_list[i]
            module_button = None
            if (i + 1) % 2 != 0:
                row_number = math.floor((i + 1) / 2)
                module_button = ModuleButton(
                    [
                        416,
                        224.13 + row_number *
                        (self.button_height + self.button_margin),
                        self.button_width,
                        self.button_height,
                    ],
                    module["name"],
                    module["icon"],
                    20,
                    border_color="#A5A5A5",
                    on_event=on_module_pressed,
                )
            else:
                row_number = (i + 1) / 2 - 1
                module_button = ModuleButton(
                    [
                        416 + self.button_width + self.button_margin,
                        224.13 + row_number *
                        (self.button_height + self.button_margin),
                        self.button_width,
                        self.button_height,
                    ],
                    module["name"],
                    module["icon"],
                    20,
                    border_color="#A5A5A5",
                    on_event=on_module_pressed,
                )
            self.menu_widget.add_child(module_button)
            self.module_btns.append(module_button)

        self.separator2 = HorizontalSeparator(
            [32, 706.13, 1300, 4]
        )
        self.separator2.line_color = "#D9D9D9"
        self.separator2.thickness = 1

        self.complete_send_button = CompleteSendButton(
            [578, 740.13, 208, 48], radius=8, on_event=on_complete_btn_pressed
        )

        self.upload_button = UploadButton(
            [1286, 740.13, 46, 48],
            radius=8,
            border_color="#5E7285",
            on_event=on_upload_btn_pressed,
        )

        self.upload_status_container = UploadStatus(
            [982, 240, 350, 450],
            radius=16,
            border_color="#303133",
            on_close=self.hide_upload_status,
        )
        self.upload_status_container.visible = False

        self.menu_widget.add_children([self.upload_status_container, self.separator2, self.complete_send_button, self.upload_button])
        self.background.add_child(self.menu_widget)

        self.add_child(self.background)

    def mark_module_complete(self, module_name):
        for module_btn in self.module_btns:
            if module_btn.name == module_name:
                module_btn.completed()

    def mark_module_incomplete(self, module_name):
        for module_btn in self.module_btns:
            if module_btn.name == module_name:
                module_btn.incomplete()

    def show_upload_status(self):
        self.upload_status_container.visible = True
        self.root_widget_tree.update(
            self.upload_status_container)

    def hide_upload_status(self, data=None):
        self.upload_status_container.visible = False
        self.root_widget_tree.update(
            self.upload_status_container)
