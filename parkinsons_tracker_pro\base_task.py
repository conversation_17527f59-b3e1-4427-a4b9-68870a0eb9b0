class BaseTask:
    def __init__(self, name, module, on_task_complete, task_params=None):
        self.name = name
        self.module = module
        self.on_task_complete = on_task_complete
        self.task_params = task_params
        self.title = None
        self.screens = []
        self.current_content = None
        self.current_screen_index = -1
        self.is_running = False
        # video_capture_task = OakdCaptureTask()
        # video_capture_task.input = {
        #     "reset_video": True,
        # }
        # video_capture_task.send()

    def initalize_screen(self):
        if len(self.screens) > 0:
            self.current_content = self.screens[0]
            self.current_screen_index = 0

    def start(self):
        print("start task")
        self.is_running = True

    def stop(self):
        self.is_running = False

    def process(self):
        pass

    def next_screen(self, data=None):
        pass
