{"service_name": "Head Pose Estimation", "developer_identifier": "78ba05ff-803b-4656-a32a-99b56be0c597", "description": "This service can be used to estimate the head pose of a person in an image. The models are optimized for macOS and coreML only.", "major_version": "0", "minor_version": "1", "platform": "macOS", "architecture": "x86_64, arm64, aarch64", "hardware_requirements": {"min_num_cpu_core": 1, "min_cpu_frequency": 0, "min_free_memory": 0, "min_free_disk": 0, "required_connected_usb_devices": []}, "is_data_source": false, "service_class": "HeadPoseEstimation", "processing_queue_size": 100, "service_processing_fps": 30, "service_id": "f0fee253-b61b-45ba-903f-591a39e13a36"}