from base_task import BaseTask
from screens.text_instruction_screen import TextInstructionScreen
from screens.facial_expressions.imitation_rating_screen import ImitationRatingScreen
from screens.facial_expressions.imitation_video_screen import ImitationVideoScreen
from screens.module_complete_screen import ModuleCompleteScreen
from screens.common_screen_element import TaskTitle
from task_data import instructions
import time
import os
import json
from service_registry import *
from pathlib import Path
import sys
from task_data import messages
from cortic_platform.sdk.app_events import ExceptionTypes

class ImitationTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("imitation", module, on_task_complete, task_params)
        self.temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/temp_collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/imitation/"
        )
        self.collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/imitation/"
        )
        Path(self.temp_collected_data_path).mkdir(parents=True, exist_ok=True)
        self.module.session.logger.info(
            "Created temp data collection dir for face imitation task"
        )
        # os.makedirs(os.path.dirname(self.collected_data_path), exist_ok=True)
        self.title = TaskTitle(
            [800, 32, 571, 42], self.module.name, self.module.icon)
        self.screens = [
            TextInstructionScreen(
                [200, 90, 1520, 950],
                instruction_text=instructions[
                    "emotional_recognition_facial_mimicry_description"
                ],
                subtitle="Emotional Recognition and Facial Mimicry",
                font_size=45,
                on_continue=self.next_screen,
            ),
            ImitationRatingScreen(
                [200, 90, 1520, 900],
                self.module.resources[
                    self.module.emotion_list[0]
                    + "_"
                    + self.module.patient_age
                    + "_"
                    + self.module.patient_gender
                    + ".jpg"
                ],
                instructions["emotional_recognition_facial_mimicry_rating"],
                expression_type="facial_mimicry_" +
                self.module.emotion_list[0],
                on_continue=self.next_screen,
                on_redo_previous=self.previous_screen
            ),
            ImitationVideoScreen(
                [200, 90, 1720, 950],
                instruction_text=instructions[
                    "emotional_recognition_facial_mimicry_recording"
                ],
                image_url=self.module.resources[
                    self.module.emotion_list[0]
                    + "_"
                    + self.module.patient_age
                    + "_"
                    + self.module.patient_gender
                    + ".jpg"
                ],
                subtitle="Emotional Recognition and Facial Mimicry",
                expression_type="facial_mimicry_" +
                self.module.emotion_list[0],
                image_width=448.3,
                image_height=560.64,
                on_start_record=self.start_recording,
                on_stop_record=self.stop_recording,
            ),
            ImitationRatingScreen(
                [200, 90, 1520, 900],
                self.module.resources[
                    self.module.emotion_list[1]
                    + "_"
                    + self.module.patient_age
                    + "_"
                    + self.module.patient_gender
                    + ".jpg"
                ],
                instructions["emotional_recognition_facial_mimicry_rating"],
                expression_type="facial_mimicry_" +
                self.module.emotion_list[1],
                on_continue=self.next_screen,
                on_redo_previous=self.previous_screen
            ),
            ImitationVideoScreen(
                [200, 90, 1720, 950],
                instruction_text=instructions[
                    "emotional_recognition_facial_mimicry_recording"
                ],
                image_url=self.module.resources[
                    self.module.emotion_list[1]
                    + "_"
                    + self.module.patient_age
                    + "_"
                    + self.module.patient_gender
                    + ".jpg"
                ],
                subtitle="Emotional Recognition and Facial Mimicry",
                expression_type="facial_mimicry_" +
                self.module.emotion_list[1],
                image_width=448.3,
                image_height=560.64,
                on_start_record=self.start_recording,
                on_stop_record=self.stop_recording,
            ),
            ImitationRatingScreen(
                [200, 90, 1520, 900],
                self.module.resources[
                    self.module.emotion_list[2]
                    + "_"
                    + self.module.patient_age
                    + "_"
                    + self.module.patient_gender
                    + ".jpg"
                ],
                instructions["emotional_recognition_facial_mimicry_rating"],
                expression_type="facial_mimicry_" +
                self.module.emotion_list[2],
                on_continue=self.next_screen,
                on_redo_previous=self.previous_screen
            ),
            ImitationVideoScreen(
                [200, 90, 1720, 950],
                instruction_text=instructions[
                    "emotional_recognition_facial_mimicry_recording"
                ],
                image_url=self.module.resources[
                    self.module.emotion_list[2]
                    + "_"
                    + self.module.patient_age
                    + "_"
                    + self.module.patient_gender
                    + ".jpg"
                ],
                subtitle="Emotional Recognition and Facial Mimicry",
                expression_type="facial_mimicry_" +
                self.module.emotion_list[2],
                image_width=448.3,
                image_height=560.64,
                on_start_record=self.start_recording,
                on_stop_record=self.stop_recording,
            ),
            ImitationRatingScreen(
                [200, 90, 1520, 900],
                self.module.resources[
                    self.module.emotion_list[3]
                    + "_"
                    + self.module.patient_age
                    + "_"
                    + self.module.patient_gender
                    + ".jpg"
                ],
                instructions["emotional_recognition_facial_mimicry_rating"],
                expression_type="facial_mimicry_" +
                self.module.emotion_list[3],
                on_continue=self.next_screen,
                on_redo_previous=self.previous_screen
            ),
            ImitationVideoScreen(
                [200, 90, 1720, 950],
                instruction_text=instructions[
                    "emotional_recognition_facial_mimicry_recording"
                ],
                image_url=self.module.resources[
                    self.module.emotion_list[3]
                    + "_"
                    + self.module.patient_age
                    + "_"
                    + self.module.patient_gender
                    + ".jpg"
                ],
                subtitle="Emotional Recognition and Facial Mimicry",
                expression_type="facial_mimicry_" +
                self.module.emotion_list[3],
                image_width=448.3,
                image_height=560.64,
                on_start_record=self.start_recording,
                on_stop_record=self.stop_recording,
            ),
            ModuleCompleteScreen(
                    [0, 100, 1920, 918],
                    instruction_text="Facial Expression Module Completed",
                    task_name=module.name,
                    asset_image=module.icon,
                    on_module_complete=self.next_screen,
                    on_redo_previous=self.previous_screen
                )
        ]
        self.video_start_time = None
        self.video_record_time = 15
        self.num_seconds = 0
        self.start_record = False
        self.module.session.logger.info(
            "("
            + self.module.session.patient_id
            + ") "
            + self.name
            + " task initialized"
        )

    def next_screen(self, data=None):
        self.current_screen_index += 1
        if self.current_screen_index >= len(self.screens):
            self.on_task_complete("imitation")
        else:
            if isinstance(self.current_content, ImitationRatingScreen):
                filename = (
                    self.temp_collected_data_path
                    + self.current_content.expression_type
                    + ".json"
                )
                os.makedirs(
                    os.path.dirname(self.temp_collected_data_path), exist_ok=True
                )
                with open(
                    filename,
                    "w+",
                ) as f:
                    f.write(
                        json.dumps(
                            {
                                "emotion": self.current_content.emotions.data,
                                "intensity": self.current_content.emotion_intensity.data,
                            }
                        )
                    )

            self.current_content = self.screens[self.current_screen_index]
            self.module.session.update_current_task_screen(self.current_content)

    def previous_screen(self, data=None):
        self.current_screen_index -= 2
        if self.current_screen_index < 0:
            self.current_screen_index = 0
            self.on_task_complete("facial_expression_text", redo=True)
        else:
            self.current_content = self.screens[self.current_screen_index]
            self.module.session.update_current_task_screen(self.current_content)

    def start_recording(self, data):
        self.current_content.show_stop_record_button()
        self.start_record = True

    def stop_recording(self, data):
        self.current_content.show_neutral_label()
        self.current_content.show_start_record_button()
        self.current_content.stat_container.time_meter.data = 0
        self.num_seconds = 0
        self.video_start_time = None
        self.start_record = False
        oakd_capture({"reset_video": True, "video_file_name": ""}).get_data()

    def process(self):
        if self.is_running:
            if isinstance(self.current_content, ImitationVideoScreen):
                if self.start_record:
                    if self.video_start_time is None:
                        self.video_start_time = time.time()
                        elapsed_time = 0
                    else:
                        elapsed_time = time.time() - self.video_start_time
                        if elapsed_time >= 1:
                            self.num_seconds += 1
                            self.video_start_time = time.time()
                            elapsed_time = 0
                        self.current_content.stat_container.time_meter.data = (
                            self.num_seconds + elapsed_time
                        )
                    if 4 < self.current_content.stat_container.time_meter.data < 11:
                        self.current_content.show_expression_label()
                    else: 
                        self.current_content.show_neutral_label()

                    if self.num_seconds >= self.video_record_time:
                        self.module.session.logger.info(
                            self.name
                            + " task ("
                            + self.current_content.expression_type
                            + ")"
                            + " complete, checking if data is recorded..."
                        )
                        self.video_start_time = None
                        self.num_seconds = 0
                        self.stop_recording(True)
                        if not os.path.isfile(
                            self.temp_collected_data_path
                            + self.current_content.expression_type
                            + ".h264"
                        ):
                            self.module.session.logger.error(
                                "Video File: "
                                + self.current_content.expression_type
                                + " for task "
                                + self.name
                                + " is not found, raising exception"
                            )
                            self.module.session.logger.error(
                                self.name + " task recorded data not found"
                            )
                            sys.stderr.write(
                                json.dumps(
                                    {
                                        "app_exception_msg": self.name
                                        + ": "
                                        + messages["recordedDataNotFound"]
                                    }
                                )
                                + "\n"
                            )
                            raise Exception(messages["recordedDataNotFound"])
                        else:
                            self.module.session.logger.info(
                                self.name + " task recorderd data found..."
                            )
                        self.next_screen()
                    else:
                        frame_data = oakd_capture({"reset_video": False, "video_file_name": self.current_content.expression_type},
                                                  service_states={"action": "capture",
                                                                  "record": self.current_content.expression_type,
                                                                  "save_path": self.temp_collected_data_path})
                        task_data = None
                        if frame_data is not None:
                            face_landmarks_result = face_landmarks(
                                {"camera_input": frame_data,
                                 "draw_landmarks": True},
                                service_states={"draw_mesh_only": True})
                            if face_landmarks_result is not None and not isinstance(face_landmarks_result, ExceptionTypes):
                                task_data = face_landmarks_result.get_data()

                        if task_data is not None:
                            self.current_content.camera_image.set_data(
                                task_data["frame"])
                else:
                    frame_data = oakd_capture({"reset_video": False, "video_file_name": ""},
                                              service_states={"action": "capture"})
                    task_data = None
                    if frame_data is not None:
                        face_landmarks_result = face_landmarks(
                            {"camera_input": frame_data,
                             "draw_landmarks": True},
                            service_states={"draw_mesh_only": True})
                        if face_landmarks_result is not None and not isinstance(face_landmarks_result, ExceptionTypes):
                            task_data = face_landmarks_result.get_data()
                    if task_data is not None:
                        self.current_content.camera_image.set_data(
                            task_data["frame"])
