from base_module import BaseModule
from tasks.facial_expression.face_calibration_task import Face<PERSON><PERSON>brationTask
from tasks.facial_expression.basic_facial_task import BasicFacialTask
from tasks.facial_expression.facial_expression_text import FacialExpressionTextTask
from tasks.facial_expression.imitation_task import ImitationTask
from utils import read_asset_image
from task_data import instructions, instruction_videos
import random
from service_registry import *
import numpy as np


class FacialExpressionsModule(BaseModule):
    def __init__(self, background, on_module_complete=None):
        super().__init__(
            "Facial Expression",
            read_asset_image("facial.png"),
            background,
            on_module_complete,
        )
        self.patient_gender = ""
        self.patient_age = ""
        self.task_list = [
            FaceCalibrationTask,
            BasicFacialTask,
            FacialExpressionTextTask,
            ImitationTask,
        ]
        self.emotion_list = list(instructions["facial_expression_text"].keys())

    def init_resources(self):
        self.session.loading_background.visible = True
        self.session.widget_tree.update(self.session.loading_background)
        self.session.app.reset_service_state("oakd_capture")
        self.session.app.reset_service_state("face_landmarks")
        # Calling the two services to activate them under task mode.
        oakd_capture({"reset_video": True,
                      "video_file_name": ""}).get_data()
        face_landmarks(
            {"camera_input": {"frame": np.zeros((720, 1280, 3), np.uint8)},
             "draw_landmarks": True}).get_data()
        self.session.loading_background.visible = False
        self.session.widget_tree.update(self.session.loading_background)

    def start(self, task_params=None):
        super().start(task_params)

    def on_task_complete(self, data=None, task_result=None, redo=False):
        super().on_task_complete()
        if (redo):
            if data == "basic_facial":
                self.redo_task(task_index=1)
                self.current_task.current_content.task_background.header_container.show_timestamp()
            if data == "facial_expression_text":
                self.emotion_list = self.emotion_list_backup
                self.redo_task(task_index=2)

                self.current_task.current_screen_index = 3
                self.current_task.current_content = self.current_task.screens[self.current_task.current_screen_index]
                self.session.update_current_task_screen(self.current_task.current_content)
                self.current_task.current_content.task_background.header_container.show_timestamp()

        else:
            self.emotion_list_backup = self.emotion_list.copy()
            random.shuffle(self.emotion_list)
            if data == "imitation":
                self.on_module_complete()
            else:
                if data == "face_calibration":
                    self.session.loading_background.visible = True
                    self.session.widget_tree.update(
                        self.session.loading_background
                    )
                    self.request_module_resouces(
                        instruction_videos["basic_facial"])
                    self.session.loading_background.visible = False
                    self.session.widget_tree.update(
                        self.session.loading_background
                    )
                elif data == "facial_expression_text":
                    self.session.loading_background.visible = True
                    self.session.widget_tree.update(
                        self.session.loading_background
                    )
                    mimic_resouces = [
                        "happy_" + self.patient_age + "_" + self.patient_gender + ".jpg",
                        "sad_" + self.patient_age + "_" + self.patient_gender + ".jpg",
                        "angry_" + self.patient_age + "_" + self.patient_gender + ".jpg",
                        "disgust_" + self.patient_age + "_" + self.patient_gender + ".jpg",
                    ]
                    self.request_module_resouces(mimic_resouces)
                    self.session.loading_background.visible = False
                    self.session.widget_tree.update(
                        self.session.loading_background
                    )
                self.next_task()
