from base_task import BaseTask
from screens.facial_expressions.face_calibration_screen import FaceCalibrationScreen
from screens.common_screen_element import TaskTitle
from task_data import messages
import time
import os
import json
from service_registry import *
from cortic_platform.sdk.app_events import ExceptionTypes

class FaceCalibrationTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("face_calibration", module, on_task_complete, task_params)
        with open(
            os.path.dirname(os.path.realpath(__file__)) + "/parameters.json"
        ) as f:
            self.params = json.load(f)
        self.current_hand = "left"
        self.title = TaskTitle(
            [800, 32, 571, 42], self.module.name, self.module.icon)
        self.face_visible = False
        self.hand_visible = False
        self.screens = [FaceCalibrationScreen([0, 100, 1920, 918])]
        self.countdown_time = 1
        self.calibrated = False
        self.showed_finish_symbol = False
        self.calibration_ok_time = time.time()
        self.hold_on_time = 1
        self.calibration_finish_time = time.time()
        self.show_finishing_symbol_time = 1

    def next_screen(self, data=None):
        self.on_task_complete("face_calibration")

    def process(self):
        if isinstance(self.current_content, FaceCalibrationScreen):
            frame_data = oakd_capture({"reset_video": False, "video_file_name": ""},
                                      service_states={"action": "capture"})
            task_data = None
            if frame_data is not None:
                face_landmark_result = face_landmarks(
                    {"camera_input": frame_data,
                     "draw_landmarks": True},
                    service_states={"draw_mesh_only": True})
                if face_landmark_result is not None and not isinstance(face_landmark_result, ExceptionTypes):
                    task_data = face_landmark_result.get_data()
            if task_data:
                if "frame" in task_data:
                    try:
                        self.current_content.main_camera_image.set_data(
                            task_data["frame"])
                    except:
                        print(task_data)
                        raise ("Task data error in face calibration task")
                    face_visible = self.is_face_visible(
                        task_data["faces"],
                        self.params["face_center_threshold"],
                        self.params["face_size_threshold"],
                    )
                    self.current_content.face_visibility_container.hint_label.data = (
                        face_visible
                    )
                    if face_visible == "OK":
                        if self.face_visible != "OK":
                            self.current_content.face_visibility_container.is_visible()
                    else:
                        if self.face_visible == "OK":
                            self.current_content.face_visibility_container.not_visible()
                    self.face_visible = face_visible

                    if face_visible != "OK":
                        self.calibrated = False
                    if face_visible == "OK":
                        if not self.calibrated:
                            self.calibrated = True
                            self.calibration_ok_time = time.time()
                        else:
                            if not self.showed_finish_symbol:
                                if (time.time() - self.calibration_ok_time) >= 1:
                                    self.current_content.task_background.show_calibration_finish()
                                    self.showed_finish_symbol = True
                                    self.calibration_finish_time = time.time()
                            else:
                                if (
                                    time.time() - self.calibration_finish_time
                                ) >= self.show_finishing_symbol_time:
                                    self.current_content.task_background.hide_calibration_finish()
                                    self.on_task_complete(
                                        "face_calibration")

    def is_face_visible(self, face, bound_threshold, size_threshold):
        bound_percentage = 0.1
        if len(face) == 0:
            return messages["face_not_visible"]
        face_center = (face[0][2] + face[0][0]) / 2
        face_area = (face[0][2] - face[0][0]) * (face[0][3] - face[0][1])
        if abs(face_center - 0.5) > bound_threshold:
            if face_center > 0.5:
                return messages["move_left"]
            else:
                return messages["move_right"]
        else:
            if (
                face[0][0] > bound_percentage
                and face[0][1] > bound_percentage
                and face[0][2] < (1 - bound_percentage)
                and face[0][3] < (1 - bound_percentage)
            ):
                if face_area > size_threshold:
                    return messages["ok"]
                else:
                    return messages["move_closer"]
            else:
                if face[0][0] <= bound_percentage:
                    return messages["move_left"]
                if face[0][1] <= bound_percentage:
                    return messages["move_lower"]
                if face[0][2] >= (1 - bound_percentage):
                    return messages["move_right"]
                if face[0][3] >= (1 - bound_percentage):
                    return messages["move_upper"]
            return messages["ok"]
