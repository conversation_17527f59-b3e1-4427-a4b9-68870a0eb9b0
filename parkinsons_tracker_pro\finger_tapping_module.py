from base_module import BaseModule
from tasks.finger_tapping.left_calibration_task import LeftCalibrationTask
from tasks.finger_tapping.right_calibration_task import RightCalibrationTask
from tasks.finger_tapping.data_collection_task import DataCollectionTask
from service_registry import *
from utils import read_asset_image
from task_data import instruction_videos
import numpy as np


class FingerTappingModule(BaseModule):
    def __init__(self, background, on_module_complete=None):
        super().__init__(
            "Finger Tapping",
            read_asset_image("finger.png"),
            background,
            on_module_complete,
        )
        self.task_list = [
            RightCalibrationTask,
            DataCollectionTask,
            LeftCalibrationTask,
            DataCollectionTask,
        ]

    def init_resources(self):
        self.session.loading_background.visible = True
        self.session.widget_tree.update(self.session.loading_background)
        self.request_module_resouces([instruction_videos["finger_tapping"]])
        self.session.app.reset_service_state("oakd_capture")
        self.session.app.reset_service_state("finger_tapping_detector")
        # Calling the two services to activate them under task mode.
        oakd_capture({"reset_video": True,
                      "video_file_name": ""}).get_data()
        finger_tapping_detector(
            {"camera_input": {"frame": np.zeros((720, 1280, 3), np.uint8)}}).get_data()
        self.session.loading_background.visible = False
        self.session.widget_tree.update(self.session.loading_background)

    def on_task_complete(self, data=None, redo=False):
        super().on_task_complete()
        if (redo):
            if data == "right_calibration":
                self.redo_task(task_index=0)
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "left_calibration":
                self.redo_task(task_index=2)
                self.current_task.current_content.task_background.header_container.show_timestamp()
        else:
            if data == "right_calibration":
                self.next_task(task_params="right")
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "left_calibration":
                self.next_task(task_params="left")
                self.current_task.current_content.task_background.header_container.show_timestamp()
            elif data == "right_collection":
                self.next_task()
                self.current_task.current_content.task_background.header_container.hide_timestamp()
            elif data == "left_collection":
                self.current_task.current_content.task_background.header_container.hide_timestamp()
                self.on_module_complete()