from base_task import BaseTask
from screens.finger_tapping.data_collection_screen import DataCollectionScreen
from screens.module_complete_screen import ModuleCompleteScreen
from screens.common_screen_element import TaskTitle
import time
import numpy as np
import json
import os
import sys
from task_data import messages
from peakdetect import peakdet
from pathlib import Path
from service_registry import *
from cortic_platform.sdk.app_events import ExceptionTypes

class DataCollectionTask(BaseTask):
    def __init__(self, module, on_task_complete, task_params=None):
        super().__init__("finger_collection", module, on_task_complete, task_params)
        with open(
            os.path.dirname(os.path.realpath(__file__)) + "/parameters.json"
        ) as f:
            self.params = json.load(f)
        self.current_hand = task_params
        self.temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/temp_collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/"
            + self.current_hand
            + "_collection/"
        )
        self.collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/collected_data/"
            + self.module.patient_id
            + "/"
            + self.module.session_time
            + "/"
            + self.current_hand
            + "_collection/"
        )
        # os.makedirs(self.temp_collected_data_path, exist_ok=True)
        # os.makedirs(self.collected_data_path, exist_ok=True)
        Path(self.temp_collected_data_path).mkdir(parents=True, exist_ok=True)
        self.module.session.logger.info(
            "Created temp data collection dir for "
            + self.current_hand
            + " finger_collection task"
        )
        # Path(self.collected_data_path).mkdir(parents=True, exist_ok=True)
        self.title = TaskTitle(
            [840, 32, 571, 42], self.module.name, self.module.icon)
        self.face_visible = False
        self.hand_visible = False
        self.tap_counter_reset = True
        self.screens = [DataCollectionScreen([0, 100, 1920, 918])]
        if (self.current_hand == 'left'):
            self.screens.append(
                ModuleCompleteScreen(
                    [0, 100, 1920, 918],
                    instruction_text="Finger Tapping Module Completed",
                    task_name=module.name,
                    asset_image=module.icon,
                    on_module_complete=self.on_module_complete,
                    on_redo_previous=self.redo_left
                ))
        self.tap_once = False
        self.num_taps = 0
        self.num_seconds = 0
        self.max_amplitude = 0
        self.frame_number = 0
        self.finger_distances = []
        self.finger_derivatives = []
        self.finger_second_derivatives = []
        self.local_maximas = []
        self.local_minimas = []
        self.local_min_derivative = 0
        self.local_max_derivate = 0
        self.current_minima_idx = -1
        self.current_maxima_idx = -1
        self.plot_first_derivative = True
        self.frames = []
        self.analytic_data = {}
        self.target_taps = 15
        self.target_seconds = 20
        self.amplitude_plot_data = [[], []]
        self.max_amplitude_data_point = 100
        oakd_capture({"reset_video": True, "video_file_name": ""}).get_data()
        self.module.session.logger.info(
            "("
            + self.module.session.patient_id
            + ") "
            + self.name
            + " task initialized"
        )

    def moving_average(self, a, n=3):
        ret = np.cumsum(a, dtype=float)
        ret[n:] = ret[n:] - ret[:-n]
        return ret[n - 1:] / n

    def moving_average_conv(self, x, w):
        return np.convolve(x, np.ones(w), "valid") / w

    def get_finger_distance(self, hand_infos):
        if len(hand_infos) == 0:
            return -1
        hand_info = None
        for hand in hand_infos:
            if hand["label"] == self.current_hand:
                hand_info = hand
        if hand_info is None:
            return -1
        return hand_info["finger_distance"]

    def count_taps(self, finger_distance):
        if self.tap_counter_reset:
            if finger_distance < self.params["finger_tap_threshold"]:
                self.num_taps += 1
                self.tap_counter_reset = False
                num_taps = self.num_taps
                self.current_content.stat_container.taps_meter.data = num_taps
        if finger_distance > self.params["finger_tap_deadzone"]:
            if not self.tap_counter_reset:
                self.tap_counter_reset = True

    def get_decrementing_amplitude(self, current_amplitude):
        value = (current_amplitude / self.max_amplitude - 1) * 100
        return value
    
    def redo_left(self, data=None):
        self.on_task_complete("left_calibration", redo=True)

    def on_module_complete(self, data=None):
        self.on_task_complete(self.current_hand + "_collection")

    def process(self):
        if isinstance(self.current_content, DataCollectionScreen):
            if self.num_taps >= self.target_taps or self.num_seconds >= self.target_seconds:
                oakd_capture(
                    {"reset_video": True, "video_file_name": ""}).get_data()
                self.module.session.logger.info(
                    self.name + " task complete, checking if data is recorded..."
                )
                if not os.path.isfile(
                    self.temp_collected_data_path
                    + self.current_hand
                    + "_finger_tapping"
                    + ".h264"
                ):
                    self.module.session.logger.error(
                        "Video File for task "
                        + self.name
                        + " is not found, raising exception"
                    )
                    self.module.session.logger.error(
                        self.name + " task recorded data not found"
                    )
                    sys.stderr.write(
                        json.dumps(
                            {
                                "app_exception_msg": self.name
                                + ": "
                                + messages["recordedDataNotFound"]
                            }
                        )
                        + "\n"
                    )
                    raise Exception(messages["recordedDataNotFound"])
                else:
                    self.module.session.logger.info(
                        self.name + " task recorderd data found..."
                    )
                if (self.current_hand == 'left'):
                    self.current_screen_index += 1
                    self.current_content = self.screens[self.current_screen_index]
                    self.current_content.task_background.header_container.menu_button.on_widget_event = self.module.session.on_back_to_menu

                    self.module.session.update_current_task_screen(self.current_content)
                else:
                    self.on_task_complete(self.current_hand + "_collection")
            else:

                frame_data = oakd_capture({"reset_video": False, "video_file_name": self.current_hand + "_finger_tapping"},
                                          service_states={"action": "capture",
                                                          "save_path": self.temp_collected_data_path})
                task_data = None
                if frame_data is not None:
                    finger_tapping_data = finger_tapping_detector(
                        {"camera_input": frame_data})
                    if finger_tapping_data is not None and not isinstance(finger_tapping_data, ExceptionTypes):
                        task_data = finger_tapping_data.get_data()
                if task_data:
                    self.frame_number += 1
                    self.current_content.main_camera_image.set_data(
                        task_data["frame"])
                    finger_distance = self.get_finger_distance(
                        task_data["hand_infos"]
                    )
                    if not self.tap_once:
                        if finger_distance != -1:
                            if (
                                finger_distance
                                < self.params["finger_tap_threshold"]
                            ):
                                self.tap_once = True
                                self.tap_counter_reset = False
                                self.current_content.start_statistic()
                                self.start_time = time.time()
                    else:
                        elapsed_time = time.time() - self.start_time
                        if elapsed_time >= 1:
                            self.num_seconds += 1
                            self.start_time = time.time()
                            elapsed_time = 0
                        self.current_content.stat_container.time_meter.data = (
                            self.num_seconds + elapsed_time
                        )
                        if finger_distance != -1:
                            self.count_taps(finger_distance)
                            if self.num_seconds == 0:
                                speed = 0
                            else:
                                speed = self.num_taps / self.num_seconds
                            self.current_content.analytic_container.speed_value.data = str(
                                round(speed, 1)
                            )
                            # self.current_content.amplitude_plot_container.amplitude_plot.data = [
                            #     [0, self.frame_number, finger_distance / 10.0]
                            # ]
                            self.amplitude_plot_data[0].append([self.frame_number, finger_distance / 10.0])
                            self.finger_distances.append(
                                finger_distance / 10.0)
                        else:
                            # self.current_content.amplitude_plot_container.amplitude_plot.data = [
                            #     [0, self.frame_number, 0.0]
                            # ]
                            self.amplitude_plot_data[0].append([self.frame_number, 0.0])
                            self.finger_distances.append(0.0)
                        if len(self.amplitude_plot_data[0]) > self.max_amplitude_data_point:
                            self.amplitude_plot_data[0].pop(0)
                        
                        self.frames.append(self.frame_number)
                        self.finger_derivatives = np.diff(
                            self.finger_distances)
                        self.finger_second_derivatives = np.diff(
                            self.finger_derivatives
                        )
                        if self.plot_first_derivative:
                            if len(self.finger_derivatives) > 0:
                                self.amplitude_plot_data[1].append([self.frame_number, self.finger_derivatives[-1]])
                                # self.current_content.amplitude_plot_container.amplitude_plot.data.append(
                                #     [
                                #         1,
                                #         self.frame_number,
                                #         self.finger_derivatives[-1],
                                #     ]
                                # )
                        else:
                            if len(self.finger_second_derivatives) > 0:
                                self.amplitude_plot_data[1].append([self.frame_number, self.finger_second_derivatives[-1]])
                                # self.current_content.amplitude_plot_container.amplitude_plot.data.append(
                                #     [
                                #         1,
                                #         self.frame_number,
                                #         self.finger_second_derivatives[-1],
                                #     ]
                                # )
                        if len(self.amplitude_plot_data[0]) > 0:
                            self.current_start_domain = self.amplitude_plot_data[0][0][0]
                            self.current_end_domain = self.amplitude_plot_data[0][-1][0]
                        self.current_content.amplitude_plot_container.amplitude_plot.set_domain(
                            self.current_start_domain, self.current_end_domain)
                        if len(self.amplitude_plot_data[1]) > self.max_amplitude_data_point:
                            self.amplitude_plot_data[1].pop(0)
                        self.current_content.amplitude_plot_container.amplitude_plot.set_data(self.amplitude_plot_data)
                        local_maximas, local_minimas = peakdet(
                            self.finger_distances, 2, x=self.frames
                        )
                        if len(local_maximas) and len(local_minimas) > 0:
                            if len(local_maximas) == len(local_minimas):
                                current_amplitude = (
                                    local_maximas[-1][1] -
                                    local_minimas[-1][1]
                                )
                                # self.current_content.amplitude_plot_container.amplitude_plot.data.append(
                                #     [
                                #         2,
                                #         int(local_maximas[-1][0]),
                                #         local_maximas[-1][1],
                                #     ]
                                # )
                                # self.current_content.amplitude_plot_container.amplitude_plot.data.append(
                                #     [
                                #         3,
                                #         int(local_minimas[-1][0]),
                                #         local_minimas[-1][1],
                                #     ]
                                # )
                                if current_amplitude > self.max_amplitude:
                                    self.max_amplitude = current_amplitude
                                    self.current_content.amplitude_container.amplitude_value.data = str(
                                        round(self.max_amplitude, 1)
                                    )
                                self.current_content.amplitude_container.decrementing_amplitude_value.data = str(
                                    round(
                                        self.get_decrementing_amplitude(
                                            current_amplitude
                                        )
                                    )
                                )

    def is_face_visible(self, face, bound_percentage):
        if len(face) == 0:
            return messages["face_not_visible"]
        if (
            face[0]["face_coordinates"][0] > bound_percentage
            and face[0]["face_coordinates"][1] > bound_percentage
            and face[0]["face_coordinates"][2] < (1 - bound_percentage)
            and face[0]["face_coordinates"][3] < (1 - bound_percentage)
        ):
            return messages["ok"]
        else:
            if face[0]["face_coordinates"][0] <= bound_percentage:
                return messages["move_left"]
            if face[0]["face_coordinates"][1] <= bound_percentage:
                return messages["move_lower"]
            if face[0]["face_coordinates"][2] >= (1 - bound_percentage):
                return messages["move_right"]
            if face[0]["face_coordinates"][3] >= (1 - bound_percentage):
                return messages["move_upper"]

    def is_hand_visible(self, hand_infos, handness, bound_percentage):
        if len(hand_infos) == 0:
            return messages["hand_not_visible"]
        hand_info = None
        for hand in hand_infos:
            if hand["label"] == handness:
                hand_info = hand
        if hand_info is None:
            return messages["wrong_hand"]
        within_bounds = True
        for location in hand_info["landmarks"]:
            if (
                location[0] / 1280.0 < bound_percentage
                or location[0] / 1280.0 >= (1 - bound_percentage)
                or location[1] / 720.0 < bound_percentage
                or location[1] / 720.0 >= (1 - bound_percentage)
            ):
                within_bounds = False
        if within_bounds:
            return messages["ok"]
        else:
            return messages["not_fully_visible"]
