from cortic_platform.sdk.ui.basic_widgets import Container
from cortic_platform.sdk.ui.misc_widgets import CircularLoader
from authentication import AwsAuthentication
from uploader import Uploader
from screens.task_background_screen import TaskBackgroundContainer
from screens.menu_screen import MenuScreen
from screens.login_screen import LoginScreen
from finger_tapping_module import FingerTappingModule
from hand_movement_module import HandMovementModule
from eye_tracking_module import EyeTrackingModule
from facial_expressions_module import FacialExpressionsModule
from datetime import datetime
import os
import shutil
import subprocess
import threading
import requests
from task_data import messages


class Session:
    def __init__(self, widget_tree, app, logger):
        self.aws_authentication = AwsAuthentication()
        self.base_url = "https://yuq8df6a4g.execute-api.ca-central-1.amazonaws.com/"
        self.widget_tree = widget_tree
        self.logger = logger
        self.app = app
        self.patient_name = ""
        self.patient_id = ""
        self.patient_gender = ""
        self.patient_age = ""
        self.session_time = ""
        self.current_task_screen = Container([0, 0, 1920, 1080])
        self.current_task_screen.visible = False
        # self.task_background.on_back_to_menu = self.on_back_to_menu

        self.login_screen = LoginScreen()
        self.login_screen.login_dialog.set_login_btn_callback(self.login)
        self.login_screen.patient_verify_screen.set_btn_callback(self.confirm_patient, self.complete_session)
        self.login_screen.popup_screen.set_ok_btn_callback(self.message_popup_close)
        
        self.module_list = [
            FingerTappingModule(self.current_task_screen, self.on_module_complete),
            HandMovementModule(self.current_task_screen, self.on_module_complete),
            EyeTrackingModule(self.current_task_screen, self.on_module_complete),
            FacialExpressionsModule(
                self.current_task_screen, self.on_module_complete),
        ]
        self.menu_screen = MenuScreen(
            radius=30,
            on_module_pressed=self.start_module,
            on_complete_btn_pressed=self.complete_session,
            on_upload_btn_pressed=self.upload_status,
        )
        self.menu_screen.module_list[0]["obj"] = self.module_list[0]
        self.menu_screen.module_list[1]["obj"] = self.module_list[1]
        self.menu_screen.module_list[2]["obj"] = self.module_list[2]
        self.menu_screen.module_list[3]["obj"] = self.module_list[3]
        self.menu_screen.visible = False

        self.uploader = Uploader(
            uploading_callback=self.uploading_callback,
            waitlist_callback=self.waitlist_callback,
            upload_status_widget=self.menu_screen.upload_status_container,
            logger=self.logger
        )
        self.uploader.run_upload = True
        self.current_module = None

        self.loading_background = Container([0, 0, 1920, 1080])
        self.loading_background.alpha = 0.4
        self.loading_background.background_color = "#000000"
        self.loading_background.visible = False
        self.loading_background.add_child(
            CircularLoader([900, 480, 120, 120]))
        self.widget_tree.add_child(self.login_screen)
        self.widget_tree.add_child(self.menu_screen)
        self.widget_tree.add_child(self.current_task_screen)
        self.widget_tree.add_child(self.loading_background)
        self.widget_tree.build()

    def get_patient_profile(self, access_token):
        headers = {
            "Authorization": "Bearer " + access_token,
            "Content-Type": "application/json",
        }
        upload_api = self.base_url + "subject/profile"

        response = requests.get(upload_api, headers=headers)
        if response.ok:
            return True, response.json()
        else:
            return False, response.json()["message"]
        
    def update_current_task_screen(self, screen):
        self.current_task_screen.clear_children()
        self.current_task_screen.add_child(screen)
        self.widget_tree.update(self.current_task_screen)

    def uploading_callback(self, item):
        self.menu_screen.upload_status_container.uploading_item.data = item

    def waitlist_callback(self, items):
        self.menu_screen.upload_status_container.waiting_items.set_data(
            self.menu_screen.upload_status_container.build_update_list(items))

    def message_popup_close(self, data):
        self.login_screen.popup_screen.visible = False
        self.widget_tree.update(self.login_screen.popup_screen)

    def get_patient_age_groud(self, age):
        if age < 40:
            return "young"
        elif age >= 40 and age < 60:
            return "middle age"
        else:
            return "old"

    def login(self, data):
        self.patient_id = self.login_screen.login_dialog.id_field.data
        if self.patient_id is not None:
            self.username = self.patient_id
            patient_id_padding = 0
            if len(self.patient_id) < 6:
                patient_id_padding = 6 - len(self.patient_id)
            self.login_id = "0" * patient_id_padding + self.patient_id
            self.loading_background.visible = True
            self.widget_tree.update(self.loading_background)
            success, result = self.aws_authentication.login(
                self.username, self.login_id
            )
            if success:
                now = datetime.now()
                year = now.strftime("%Y")
                month = now.strftime("%b")
                day = now.strftime("%d")
                time = now.strftime("%I:%M %p")
                success, patient_profile = self.get_patient_profile(result)
                if success:
                    self.patient_name = patient_profile["name"]
                    self.login_screen.patient_verify_screen.confirm_text.data = (
                        "Are you **" + self.patient_name + "**?"
                    )
                    patient_gender = patient_profile["gender"]
                    if patient_gender == "Male":
                        self.patient_gender = "m"
                    else:
                        self.patient_gender = "f"
                    b_year, b_month, b_day = patient_profile["birthday"].split(
                        "/")
                    self.patient_age = self.get_patient_age_groud(
                        datetime.now().year - int(b_year)
                    )

                self.loading_background.visible = False
                self.widget_tree.update(
                    self.loading_background)
                self.session_time = month + " " + \
                    day + " " + year + " - " + str(time)
                self.uploader.access_token = result
                self.login_screen.patient_verify_screen.visible = True
                self.widget_tree.update(
                    self.login_screen.patient_verify_screen)
                self.login_screen.login_dialog.visible = False
                self.widget_tree.update(self.login_screen.login_dialog)
            else:
                self.loading_background.visible = False
                self.widget_tree.update(
                    self.loading_background)
                self.login_screen.popup_screen.message_text.data = result
                self.login_screen.popup_screen.visible = True
                self.widget_tree.update(self.login_screen.popup_screen)
        else:
            self.loading_background.visible = False
            self.widget_tree.update(self.loading_background)
            self.login_screen.popup_screen.message_text.data = messages["enterPatientPIN"]
            self.login_screen.popup_screen.visible = True
            self.widget_tree.update(self.login_screen.popup_screen)

    def confirm_patient(self, data):
        for module in self.module_list:
            self.menu_screen.mark_module_incomplete(module.name)
        self.login_screen.patient_verify_screen.visible = False
        self.widget_tree.update(self.login_screen.patient_verify_screen)
        # self.task_background.header_container.time_string = self.session_time
        self.menu_screen.patient_title.data = self.patient_name
        self.login_screen.visible = False
        self.widget_tree.update(self.login_screen)
        self.menu_screen.visible = True
        self.widget_tree.update(self.menu_screen)
        # self.task_background.header_container.patient_name = self.patient_name

    def start_module(self, module_name):
        for module in self.module_list:
            if module.name == module_name:
                self.current_module = module
                self.current_module.session = self
                self.current_module.patient_name = self.patient_name
                self.current_module.patient_id = self.patient_id
                self.current_module.patient_gender = self.patient_gender
                self.current_module.patient_age = self.patient_age
                self.current_module.session_time = self.session_time
                self.current_module.access_token = self.uploader.access_token
                self.current_module.init_resources()

                self.current_module.uploader = self.uploader
                self.menu_screen.hide_upload_status()
                self.menu_screen.visible = False
                self.widget_tree.update(self.menu_screen)
                self.login_screen.visible = False
                self.widget_tree.update(self.login_screen)
                self.current_task_screen.visible = True
                self.widget_tree.update(self.current_task_screen)
                self.menu_screen.mark_module_incomplete(
                    self.current_module.name)
                self.current_module.start()

    def on_back_to_menu(self, data=None):
        self.process_module_data(
            self.current_module.name.lower().replace(" ", "_"))
        self.current_module.stop()
        # self.task_background.reset()
        self.current_task_screen.visible = False
        self.widget_tree.update(self.current_task_screen)
        self.menu_screen.visible = True
        self.widget_tree.update(self.menu_screen)
        self.menu_screen.menu_widget.visible = True
        self.widget_tree.update(self.menu_screen.menu_widget)
        
    def convert_video_format(self, path):
        for f in os.listdir(path):
            ext = os.path.splitext(f)[-1].lower()
            if ext == ".h264":
                commands = [
                    "/opt/homebrew/bin/ffmpeg",
                    "-y",
                    "-framerate",
                    "60",
                    "-i",
                    path + f,
                    "-pix_fmt",
                    "yuv420p",
                    "-c",
                    "copy",
                    path + f.replace(".h264", ".mp4"),
                ]
                if subprocess.run(commands).returncode == 0:
                    self.logger.info(
                        f + " converted to mp4 successfully, removing " + path + f
                    )
                    os.remove(path + f)
                else:
                    self.logger.error("There was an error converting " + f)

    def process_data_func(self, temp_collected_data_path, path_for_upload):
        task_path = temp_collected_data_path + self.patient_id + "/" + self.session_time
        if os.path.exists(task_path):
            for task in os.listdir(task_path):
                if task != ".DS_Store":
                    try:
                        self.convert_video_format(task_path + "/" + task + "/")
                    except:
                        self.logger.error(
                            "Failed to convert this video, skipping.")
            if not os.path.exists(path_for_upload):
                shutil.move(temp_collected_data_path, path_for_upload)
            else:
                if os.path.exists(path_for_upload + self.patient_id):
                    shutil.move(
                        temp_collected_data_path
                        + self.patient_id
                        + "/"
                        + self.session_time,
                        path_for_upload + self.patient_id,
                    )
                else:
                    shutil.move(
                        temp_collected_data_path + self.patient_id, path_for_upload
                    )
            self.uploader.scan_file_for_upload()

    def process_module_data(self, module_name):
        temp_collected_data_path = (
            os.path.dirname(os.path.realpath(__file__))
            + "/tasks/"
            + module_name
            + "/temp_collected_data/"
        )
        path_for_upload = (
            os.path.dirname(os.path.realpath(__file__))
            + "/tasks/"
            + module_name
            + "/collected_data/"
        )
        process_thread = threading.Thread(
            target=self.process_data_func,
            args=(
                temp_collected_data_path,
                path_for_upload,
            ),
            daemon=True,
        )
        process_thread.start()

    def on_module_complete(self):
        self.menu_screen.mark_module_complete(self.current_module.name)
        self.on_back_to_menu()

    def upload_status(self, data):
        self.menu_screen.show_upload_status()

    def complete_session(self, data):
        self.patient_id = None
        self.login_screen.login_dialog.id_field.data = None
        self.patient_name = ""
        self.login_screen.patient_verify_screen.visible = False
        self.widget_tree.update(self.login_screen.patient_verify_screen)
        self.menu_screen.visible = False
        self.widget_tree.update(self.menu_screen)
        self.login_screen.visible = True
        self.widget_tree.update(self.login_screen)
        self.login_screen.login_dialog.visible = True
        self.widget_tree.update(self.login_screen.login_dialog)
        # self.task_background.header_container.patient_name = ""

    def generate_report(self):
        pass

    def upload_recordings(self):
        pass
