"""
This file is automatically generated from the service_registry.json by our framework 
and should not be manually modified by developers. Any changes made to this file may 
be overwritten during future auto-generation processes, and the functionality of the 
system may be negatively affected. Instead, please follow the appropriate documentation 
and guidelines to customize or extend the functionality of the framework by updating the 
service_registry.json file as needed.
"""
from cortic_platform.sdk.app_manager import service_handle
@service_handle
def face_landmarks(task_data, device_name='', on_result=None, timeout=None, service_states={}):
  pass

@service_handle
def oakd_capture(task_data, device_name='', on_result=None, timeout=None, service_states={}):
  pass

@service_handle
def finger_tapping_detector(task_data, device_name='', on_result=None, timeout=None, service_states={}):
  pass

@service_handle
def iris_detection(task_data, device_name='', on_result=None, timeout=None, service_states={}):
  pass

@service_handle
def head_pose_estimation(task_data, device_name='', on_result=None, timeout=None, service_states={}):
  pass

