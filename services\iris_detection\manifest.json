{"service_name": "Iris Detection", "developer_identifier": "78ba05ff-803b-4656-a32a-99b56be0c597", "description": "This service can detect iris in the image and return the bounding box of the iris. The models are optimized for macOS and coreML only.", "major_version": "0", "minor_version": "1", "platform": "macOS", "architecture": "x86_64, arm64, aarch64", "hardware_requirements": {"min_num_cpu_core": 1, "min_cpu_frequency": 0, "min_free_memory": 0, "min_free_disk": 0, "required_connected_usb_devices": []}, "is_data_source": false, "service_class": "IrisDetection", "processing_queue_size": 100, "service_processing_fps": 30}