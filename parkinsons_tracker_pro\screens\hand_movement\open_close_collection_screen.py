from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image, VerticalSeparator
from cortic_platform.sdk.ui.misc_widgets import CircularCounter
from utils import read_asset_image
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)) + "/../..")
from screens.task_background_screen import TaskBackgroundContainer
from screens.common_screen_element import TaskTitle
from task_data import messages


class OpenCloseStatContainer(Container):
    def __init__(self, rect=[0, 0, 685, 264], radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.radius = 20
        self.alpha = 1

        self.time_label = Label(
            [30, 30, 311.5, 41],
            data=messages["time"],
        )
        self.time_label.font_size = 30
        self.time_label.alignment = "center"
        self.time_label.font_weight = "bold"

        self.time_meter = CircularCounter(
            [111.75, 86, 150, 150]
        )
        self.time_meter.counter_font_size = 79
        self.time_meter.end_value = 20
        self.time_meter.ring_thickness = 11

        self.separator = VerticalSeparator(
            [341.5, 30, 2, 204])
        self.separator.line_color = "#D9D9D9"

        self.taps_label = Label(
            [343.5, 30, 311.5, 37],
            data=messages["movements"],
        )
        self.taps_label.font_size = 30
        self.taps_label.alignment = "center"
        self.taps_label.font_weight = "bold"

        self.taps_meter = CircularCounter(
            [425.25, 86, 150, 150]
        )
        self.taps_meter.counter_font_size = 79
        self.taps_meter.end_value = 15
        self.taps_meter.ring_thickness = 11

        self.add_children(
            [self.time_label, self.time_meter, self.separator, self.taps_label, self.taps_meter]
        )


class OpenCloseCollectionScreen(Container):
    def __init__(self, rect=[0, 0, 1920, 918], on_complete=None, radius=0, border_color=None):
        super().__init__([0, 0, 1920, 1080])
        
        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name="Hand Movement",
                                                           task_icon=read_asset_image("hand.png")))
        self.content = Container([0, 100, 1920, 918])
        self.content.corner_radius = radius
        self.content.border_color = border_color
        self.content.alpha = 0

        self.ready_label = Label(
            [0, 10, 1920, 59],
            data=messages["close_hand_ready"],
        )
        self.ready_label.font_size = 40
        self.ready_label.alignment = "center"
        self.ready_label.font_weight = "bold"

        self.keep_tapping_label = Label(
            [0, 10, 1920, 59],
            data=messages["open_close_hint"],
        )
        self.keep_tapping_label.font_size = 40
        self.keep_tapping_label.alignment = "center"
        self.keep_tapping_label.font_weight = "bold"
        self.keep_tapping_label.visible = False

        self.stat_container = OpenCloseStatContainer([617.5, 80, 685, 264])
        
        self.main_camera_image = Image(
            [472.275, 355, 975.45, 549.15]
        )  # full length 1089.17
        self.main_camera_image.alpha = 1
        self.main_camera_image.corner_radius = 10
        self.main_camera_image.border_color = "#000000"

        self.content.add_children([self.ready_label, self.keep_tapping_label, self.stat_container, self.main_camera_image])

        self.task_background.update_content(self.content)
        self.add_child(self.task_background)

    def start_statistic(self):
        self.keep_tapping_label.visible = True
        self.root_widget_tree.update(self.keep_tapping_label)
        self.ready_label.visible = False
        self.root_widget_tree.update(self.ready_label)
