from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image
from common_utils import read_asset_image

class InstructionTitle(Container):
    def __init__(self, rect=[0, 0, 1471.76, 100], task_name="<Task Name>", radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1

        self.title = Label(
            [0, 0, 1471.76, 100],
            data="How to perform the " + task_name + " task",
        )
        self.title.alignment = "center"
        self.title.font_size = 30

        self.add_child(self.title)


class TaskTitle(Container):
    def __init__(self, rect=[0, 0, 571, 42], task_name="Task Name", task_icon=read_asset_image("finger.png"), radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.alpha = 1

        self.task_icon = Image([0, 0, 37, 38], data=task_icon)

        self.task_name = Label(
            [45, 0, 400, 42], data=task_name
        )
        
        self.task_name.font_size = 30
        self.task_name.font_weight = "bold"

        self.add_children([self.task_icon, self.task_name])

background_image = Image(
            [0, 0, 1920, 1080],
            data=read_asset_image("background.png")
        )
