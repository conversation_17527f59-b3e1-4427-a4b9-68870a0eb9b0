import requests
import json
import os
import threading
import time
import collections
import pickle
import sys
import shutil
import subprocess
from authentication import AwsAuthentication


class Uploader:
    def __init__(self, uploading_callback, waitlist_callback, upload_status_widget, logger):
        self.aws_authentication = AwsAuthentication()
        self.upload_status_widget = upload_status_widget
        self.logger = logger
        self.access_token = ""
        self.base_url = "https://yuq8df6a4g.execute-api.ca-central-1.amazonaws.com/"
        self.upload_queue = collections.deque()
        self.files_to_upload = []
        self.current_uploading_file = ""
        # self.scan_file_for_upload()
        self.run_upload = False
        self.uploading_callback = uploading_callback
        self.waitlist_callback = waitlist_callback
        self.uploading = False
        self.process_partial_data()
        self.item_list_lock = threading.Lock()
        self.upload_thread = threading.Thread(
            target=self.upload_func, daemon=True)
        self.upload_thread.start()

    def upload_elasped_time_counter_func(self):
        elapsed_time = 0
        while self.uploading:
            self.upload_status_widget.elapsed_time.data = str(elapsed_time)
            time.sleep(1)
            elapsed_time += 1

    def process_partial_data(self):
        for module in os.listdir(
            os.path.dirname(os.path.realpath(__file__)) + "/tasks/"
        ):
            if module != ".DS_Store":
                if "temp_collected_data" in os.listdir(
                    os.path.dirname(os.path.realpath(
                        __file__)) + "/tasks/" + module
                ):
                    if not os.path.exists(
                        os.path.dirname(os.path.realpath(__file__))
                        + "/tasks/"
                        + module
                        + "/collected_data/"
                    ):
                        shutil.move(
                            os.path.dirname(os.path.realpath(__file__))
                            + "/tasks/"
                            + module
                            + "/temp_collected_data/",
                            os.path.dirname(os.path.realpath(__file__))
                            + "/tasks/"
                            + module
                            + "/collected_data/",
                        )
                    else:
                        for patient_id in os.listdir(
                            os.path.dirname(os.path.realpath(__file__))
                            + "/tasks/"
                            + module
                            + "/temp_collected_data/"
                        ):
                            if patient_id != ".DS_Store":
                                if not os.path.exists(
                                    os.path.dirname(os.path.realpath(__file__))
                                    + "/tasks/"
                                    + module
                                    + "/collected_data/"
                                    + patient_id
                                ):
                                    shutil.move(
                                        os.path.dirname(
                                            os.path.realpath(__file__))
                                        + "/tasks/"
                                        + module
                                        + "/temp_collected_data/"
                                        + patient_id,
                                        os.path.dirname(
                                            os.path.realpath(__file__))
                                        + "/tasks/"
                                        + module
                                        + "/collected_data/",
                                    )
                                else:
                                    for session in os.listdir(
                                        os.path.dirname(
                                            os.path.realpath(__file__))
                                        + "/tasks/"
                                        + module
                                        + "/temp_collected_data/"
                                        + patient_id
                                    ):
                                        if session != ".DS_Store":
                                            if not os.path.exists(
                                                os.path.dirname(
                                                    os.path.realpath(__file__)
                                                )
                                                + "/tasks/"
                                                + module
                                                + "/collected_data/"
                                                + patient_id
                                                + "/"
                                                + session
                                            ):
                                                shutil.move(
                                                    os.path.dirname(
                                                        os.path.realpath(
                                                            __file__)
                                                    )
                                                    + "/tasks/"
                                                    + module
                                                    + "/temp_collected_data/"
                                                    + patient_id
                                                    + "/"
                                                    + session,
                                                    os.path.dirname(
                                                        os.path.realpath(
                                                            __file__)
                                                    )
                                                    + "/tasks/"
                                                    + module
                                                    + "/collected_data/"
                                                    + patient_id,
                                                )
                    if os.path.exists(
                        os.path.dirname(os.path.realpath(__file__))
                        + "/tasks/"
                        + module
                        + "/temp_collected_data/"
                    ):
                        shutil.rmtree(
                            os.path.dirname(os.path.realpath(__file__))
                            + "/tasks/"
                            + module
                            + "/temp_collected_data/"
                        )

    def scan_file_for_upload(self):
        for module in os.listdir(
            os.path.dirname(os.path.realpath(__file__)) + "/tasks/"
        ):
            if module != ".DS_Store":
                if "collected_data" in os.listdir(
                    os.path.dirname(os.path.realpath(
                        __file__)) + "/tasks/" + module
                ):
                    for patient_id in os.listdir(
                        os.path.dirname(os.path.realpath(__file__))
                        + "/tasks/"
                        + module
                        + "/collected_data/"
                    ):
                        if patient_id != ".DS_Store":
                            num_session = 0
                            for session in os.listdir(
                                os.path.dirname(os.path.realpath(__file__))
                                + "/tasks/"
                                + module
                                + "/collected_data/"
                                + patient_id
                            ):
                                num_task_data = 0
                                if session != ".DS_Store":
                                    num_session += 1
                                    for task in os.listdir(
                                        os.path.dirname(
                                            os.path.realpath(__file__))
                                        + "/tasks/"
                                        + module
                                        + "/collected_data/"
                                        + patient_id
                                        + "/"
                                        + session
                                    ):
                                        if task != ".DS_Store":
                                            num_task_data += 1
                                            task_files = []
                                            for f in os.listdir(
                                                os.path.dirname(
                                                    os.path.realpath(__file__)
                                                )
                                                + "/tasks/"
                                                + module
                                                + "/collected_data/"
                                                + patient_id
                                                + "/"
                                                + session
                                                + "/"
                                                + task
                                            ):
                                                if f != ".DS_Store":
                                                    path = (
                                                        os.path.dirname(
                                                            os.path.realpath(
                                                                __file__)
                                                        )
                                                        + "/tasks/"
                                                        + module
                                                        + "/collected_data/"
                                                        + patient_id
                                                        + "/"
                                                        + session
                                                        + "/"
                                                        + task
                                                        + "/"
                                                    )
                                                    ext = os.path.splitext(f)[
                                                        -1
                                                    ].lower()
                                                    if ext == ".h264":
                                                        commands = [
                                                            "/opt/homebrew/bin/ffmpeg",
                                                            "-y",
                                                            "-framerate",
                                                            "60",
                                                            "-i",
                                                            path + f,
                                                            "-pix_fmt",
                                                            "yuv420p",
                                                            "-c",
                                                            "copy",
                                                            path
                                                            + f.replace(
                                                                ".h264", ".mp4"
                                                            ),
                                                        ]
                                                        if (
                                                            subprocess.run(
                                                                commands
                                                            ).returncode
                                                            == 0
                                                        ):
                                                            self.logger.info(
                                                                f
                                                                + " converted to mp4 successfully, removing"
                                                                + path
                                                                + f,
                                                            )
                                                            os.remove(path + f)
                                                            f = f.replace(
                                                                ".h264", ".mp4"
                                                            )
                                                        else:
                                                            self.logger.info(
                                                                "There was an error converting"
                                                                + f,
                                                            )
                                                    task_files.append(f)
                                            if len(task_files) > 0:
                                                for tf in task_files:
                                                    file_description = (
                                                        patient_id
                                                        + "/"
                                                        + session
                                                        + "/"
                                                        + module
                                                        + "/"
                                                        + task
                                                        + "/"
                                                        + tf
                                                    )
                                                    self.item_list_lock.acquire()
                                                    if (
                                                        file_description
                                                        not in self.files_to_upload
                                                        and self.current_uploading_file
                                                        != file_description
                                                    ):
                                                        self.files_to_upload.append(
                                                            file_description
                                                        )
                                                        self.upload_queue.append(
                                                            {
                                                                "patient_id": patient_id,
                                                                "files": task_files,
                                                                "task_name": task,
                                                                "module_name": module,
                                                                "session_time": session,
                                                            }
                                                        )
                                                    self.item_list_lock.release()
                                            else:
                                                shutil.rmtree(
                                                    (
                                                        os.path.dirname(
                                                            os.path.realpath(
                                                                __file__)
                                                        )
                                                        + "/tasks/"
                                                        + module
                                                        + "/collected_data/"
                                                        + patient_id
                                                        + "/"
                                                        + session
                                                        + "/"
                                                        + task
                                                    )
                                                )
                                    if num_task_data == 0:
                                        shutil.rmtree(
                                            (
                                                os.path.dirname(
                                                    os.path.realpath(__file__)
                                                )
                                                + "/tasks/"
                                                + module
                                                + "/collected_data/"
                                                + patient_id
                                                + "/"
                                                + session
                                            )
                                        )
                            if num_session == 0:
                                shutil.rmtree(
                                    os.path.dirname(os.path.realpath(__file__))
                                    + "/tasks/"
                                    + module
                                    + "/collected_data/"
                                    + patient_id
                                )
        self.waitlist_callback(self.files_to_upload)

    def notify_task_complete(self, patient_id, task_name, module_name, session_time):
        headers = {
            "Authorization": "Bearer " + self.access_token,
            "Content-Type": "application/json",
        }
        upload_api = self.base_url + "subject/taskcomplete"
        data = {
            "subject_id": patient_id,
            "session_time": str(session_time),
            "module_name": module_name,
            "task_name": task_name,
        }
        response = requests.post(upload_api, headers=headers, json=data)
        if response.ok:
            return True, response.json()
        else:
            return False, response.json()["message"]

    def request_upload_url(
        self, patient_id, filename, task_name, module_name, session_time
    ):
        headers = {
            "Authorization": "Bearer " + self.access_token,
            "Content-Type": "application/json",
        }
        upload_api = self.base_url + "subject/upload"
        payload = {
            "subject_id": patient_id,
            "session_time": str(session_time),
            "module_name": module_name,
            "task_name": task_name,
            "file_name": filename,
        }
        response = requests.get(upload_api, headers=headers, params=payload)
        if response.ok:
            return True, response.json()
        else:
            return False, response.json()["message"]

    def get_access_token(self, patient_id):
        username = patient_id
        if len(patient_id) < 6:
            patient_id_padding = 6 - len(patient_id)
        login_id = "0" * patient_id_padding + patient_id
        success = False
        result = ""
        while not success:
            success, result = self.aws_authentication.login(username, login_id)
            if not success:
                self.logger.error(
                    "Getting access token with patient ID: "
                    + username
                    + " failed, retrying in 5 sec...",
                )
                time.sleep(5)
        return result

    def upload_func(self):
        while True:
            if self.upload_queue and self.run_upload:
                upload_item = self.upload_queue.popleft()
                this_upload_success = True
                for f in upload_item["files"]:
                    file_path = (
                        os.path.dirname(os.path.realpath(__file__))
                        + "/tasks/"
                        + upload_item["module_name"]
                        + "/collected_data/"
                        + upload_item["patient_id"]
                        + "/"
                        + upload_item["session_time"]
                        + "/"
                        + upload_item["task_name"]
                        + "/"
                        + f
                    )
                    file_exist = os.path.exists(file_path)
                    if file_exist:
                        # check file exist
                        if self.access_token == "":
                            self.access_token = self.get_access_token(
                                upload_item["patient_id"]
                            )
                        request_success = False
                        while not request_success:
                            request_success, result = self.request_upload_url(
                                upload_item["patient_id"],
                                f,
                                upload_item["task_name"],
                                upload_item["module_name"],
                                upload_item["session_time"],
                            )
                            if not request_success:
                                self.logger.error(
                                    "Failed to request upload url for: "
                                    + file_path
                                    + " retrying in 5 sec...",
                                )
                                time.sleep(5)
                                self.access_token = self.get_access_token(
                                    upload_item["patient_id"]
                                )
                        if request_success:
                            upload_file = open(file_path, "rb")
                            if sys.getsizeof(upload_file) > 0:
                                file_description = (
                                    upload_item["patient_id"]
                                    + "/"
                                    + upload_item["session_time"]
                                    + "/"
                                    + upload_item["module_name"]
                                    + "/"
                                    + upload_item["task_name"]
                                    + "/"
                                    + f
                                )
                                self.current_uploading_file = file_description
                                self.uploading_callback(
                                    self.current_uploading_file)
                                elpased_time_thread = threading.Thread(
                                    target=self.upload_elasped_time_counter_func,
                                    daemon=True,
                                )
                                self.item_list_lock.acquire()
                                self.files_to_upload.remove(file_description)
                                self.waitlist_callback(self.files_to_upload)
                                self.item_list_lock.release()
                                self.uploading = True
                                elpased_time_thread.start()
                                self.logger.info(
                                    "Uploading: " + file_description)
                                r = requests.put(
                                    result,
                                    data=upload_file,
                                    headers={
                                        "Content-Length": str(sys.getsizeof(upload_file))
                                    },
                                )
                                self.uploading = False
                                elpased_time_thread.join()
                                self.upload_status_widget.elapsed_time.data = ""
                                self.current_uploading_file = ""
                                self.uploading_callback(
                                    self.current_uploading_file)
                                if r.ok:
                                    temp_location = (
                                        os.path.dirname(
                                            os.path.realpath(__file__))
                                        + "/temp/"
                                        + upload_item["module_name"]
                                        + "/collected_data/"
                                        + upload_item["patient_id"]
                                        + "/"
                                        + upload_item["session_time"]
                                        + "/"
                                        + upload_item["task_name"]
                                        + "/"
                                    )
                                    isExist = os.path.exists(temp_location)
                                    if not isExist:
                                        os.makedirs(
                                            os.path.dirname(temp_location), exist_ok=True
                                        )
                                    original = (
                                        os.path.dirname(
                                            os.path.realpath(__file__))
                                        + "/tasks/"
                                        + upload_item["module_name"]
                                        + "/collected_data/"
                                        + upload_item["patient_id"]
                                        + "/"
                                        + upload_item["session_time"]
                                        + "/"
                                        + upload_item["task_name"]
                                        + "/"
                                        + f
                                    )
                                    shutil.move(original, temp_location)
                                    self.logger.info(
                                        "Moved " + f + " to temp location",
                                    )
                                if not r.ok:
                                    self.logger.error(
                                        "Error in uploading file: "
                                        + self.current_uploading_file
                                        + ", putting back to queue",
                                    )
                                    self.logger.error(str(r.content))
                                    self.item_list_lock.acquire()
                                    self.files_to_upload.append(
                                        file_description)
                                    self.waitlist_callback(
                                        self.files_to_upload)
                                    self.item_list_lock.release()
                                    this_upload_success = False
                            else:
                                self.logger.info(
                                    "File size is 0, likely conversion failed, skipping...",
                                )
                                self.item_list_lock.acquire()
                                self.files_to_upload.append(file_description)
                                self.waitlist_callback(self.files_to_upload)
                                self.item_list_lock.release()
                                this_upload_success = False
                        else:
                            self.logger.info(str(result))
                            this_upload_success = False
                if this_upload_success:
                    temp_task_folder = (
                        os.path.dirname(os.path.realpath(__file__))
                        + "/temp/"
                        + upload_item["module_name"]
                        + "/collected_data/"
                        + upload_item["patient_id"]
                        + "/"
                        + upload_item["session_time"]
                        + "/"
                        + upload_item["task_name"]
                    )
                    isExist = os.path.exists(temp_task_folder)
                    if isExist:
                        shutil.rmtree(temp_task_folder)
                    s, res = self.notify_task_complete(
                        upload_item["patient_id"],
                        upload_item["task_name"],
                        upload_item["module_name"],
                        upload_item["session_time"],
                    )
                    if s:
                        self.logger.info(
                            "Finished uploading all files for task: "
                            + upload_item["task_name"],
                        )
                    else:
                        self.logger.info(str(res))
                        self.upload_queue.append(upload_item)
                else:
                    self.upload_queue.append(upload_item)
            else:
                if not self.upload_queue:
                    self.scan_file_for_upload()
                time.sleep(1)
