from cortic_platform.sdk.ui.basic_widgets import Container, Label, Image, VerticalSeparator
from cortic_platform.sdk.ui.misc_widgets import CircularCounter
from cortic_platform.sdk.ui.chart_widgets import LineChart
from utils import read_asset_image
import sys
import os
sys.path.append(os.path.dirname(os.path.realpath(__file__)) + "/../..")
from screens.task_background_screen import TaskBackgroundContainer
from screens.common_screen_element import TaskTitle
from task_data import messages


class FingetTappingStatContainer(Container):
    def __init__(self, rect=[0, 0, 685, 264], radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.radius = 20
        self.alpha = 1

        self.time_label = Label(
            [30, 30, 311.5, 41],
            data=messages["time"],
        )
        self.time_label.font_size = 30
        self.time_label.font_weight = "bold"
        self.time_label.alignment = "center"

        self.time_meter = CircularCounter(
            [111.75, 86, 150, 151]
        )
        self.time_meter.radius = 70
        self.time_meter.counter_font_size = 79
        self.time_meter.end_value = 20
        self.time_meter.ring_thickness = 14

        self.separator = VerticalSeparator(
            [341.5, 30, 2, 205])
        self.separator.line_color = "#D9D9D9"

        self.taps_label = Label(
            [343.5, 30, 311.5, 37],
            data=messages["taps"],
        )
        self.taps_label.font_size = 30
        self.taps_label.font_weight = "bold"
        self.taps_label.alignment = "center"

        self.taps_meter = CircularCounter(
            [425.25, 86, 150, 151]
        )
        self.taps_meter.radius = 70
        self.taps_meter.counter_font_size = 79
        self.taps_meter.ring_thickness = 14
        self.taps_meter.end_value = 15

        self.add_children(
            [
                self.time_label,
                self.time_meter,
                self.separator,
                self.taps_label,
                self.taps_meter,
            ]
        )


class FingetTappingAnalyticContainer(Container):
    def __init__(self, rect=[0, 0, 685, 211], radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.radius = 20
        self.alpha = 1

        self.speed_label = Label(
            [30, 30, 207, 37],
            data=messages["speed"],
        )
        self.speed_label.font_size = 30
        self.speed_label.font_weight = "bold"
        self.speed_label.alignment = "center"

        self.speed_value = Label(
            [30, 86, 207, 96],
            data="--",
        )
        self.speed_value.font_size = 79
        self.speed_value.font_weight = "bold"
        self.speed_value.alignment = "center"

        self.separator1 = VerticalSeparator([237, 30, 2, 151])
        self.separator1.line_color = "#D9D9D9"

        self.halt_label = Label(
            [239, 30, 207, 37],
            data=messages["halt"],
        )
        self.halt_label.font_size = 30
        self.halt_label.font_weight = "bold"
        self.halt_label.alignment = "center"
        
        self.halt_value = Label(
            [239, 86, 207, 95],
            data="--",
        )
        self.halt_value.font_size = 79
        self.halt_value.font_weight = "bold"
        self.halt_value.alignment = "center"

        self.separator2 = VerticalSeparator([446, 30, 2, 151])
        self.separator2.line_color = "#D9D9D9"

        self.hestitation_label = Label(
            [448, 30, 207, 37],
            data=messages["hestitation"],
        )
        self.hestitation_label.font_size = 30
        self.hestitation_label.font_weight = "bold"
        self.hestitation_label.alignment = "center"

        self.hestitation_value = Label(
            [448, 86, 207, 96],
            data="--",
        )
        self.hestitation_value.font_size = 79
        self.hestitation_value.font_weight = "bold"
        self.hestitation_value.alignment = "center"

        self.add_children(
            [
                self.speed_label,
                self.speed_value,
                self.separator1,
                self.halt_label,
                self.halt_value,
                self.separator2,
                self.hestitation_label,
                self.hestitation_value,
            ]
        )


class FingetTappingAmplitudeContainer(Container):
    def __init__(self, rect=[0, 0, 685, 201], radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.radius = 20
        self.alpha = 1

        self.amplitude_label = Label(
            [50, 30, 207, 37],
            data=messages["amplitude"],
        )
        self.amplitude_label.alignment = "center"
        self.amplitude_label.font_size = 30
        self.amplitude_label.font_weight = "bold"

        self.amplitude_value = Label(
            [50, 75, 207, 97],
            data="    --",
        )
        self.amplitude_value.alignment = "left"
        self.amplitude_value.font_size = 79
        self.amplitude_value.font_weight = "bold"

        self.amplitude_unit = Label(
            [50, 75, 207, 96],
            data=messages["finger_distance_unit"],
        )
        self.amplitude_unit.alignment = "right"
        self.amplitude_unit.font_size = 30
        self.amplitude_unit.font_weight = "bold"

        self.separator = VerticalSeparator([277, 30, 2, 141])
        self.separator.line_color = "#D9D9D9"

        self.decrementing_amplitude_label = Label(
            [299, 30, 370, 37],
            data=messages["decr_amplitude"],
        )
        self.decrementing_amplitude_label.alignment = "left"
        self.decrementing_amplitude_label.font_size = 30
        self.decrementing_amplitude_label.font_weight = "bold"

        self.decrementing_amplitude_value = Label(
            [438.5, 76, 200, 96],
            data="--",
        )
        self.decrementing_amplitude_value.alignment = "left"
        self.decrementing_amplitude_value.font_size = 79
        self.decrementing_amplitude_value.font_weight = "bold"

        self.decrementing_amplitude_unit = Label(
            [438.5, 76, 200, 95],
            data="%",
        )
        self.decrementing_amplitude_unit.alignment = "right"
        self.decrementing_amplitude_unit.font_size = 30
        self.decrementing_amplitude_unit.font_weight = "bold"

        self.add_children(
            [
                self.amplitude_label,
                self.amplitude_value,
                self.amplitude_unit,
                self.separator,
                self.decrementing_amplitude_label,
                self.decrementing_amplitude_value,
                self.decrementing_amplitude_unit,
            ]
        )


class FingetTappingAmplitudePlotContainer(Container):
    def __init__(self, rect=[0, 0, 1094, 312], radius=0, border_color=None):
        super().__init__(rect)
        self.corner_radius = radius
        self.border_color = border_color
        self.radius = 20
        self.alpha = 1

        self.amplitude_label = Label(
            [30, 20, 250, 37],
            data=messages["amplitude"],
        )
        self.amplitude_label.font_size = 30
        self.amplitude_label.font_weight = "bold"

        self.amplitude_plot = LineChart(
            [30, 76, 1034, 277], num_plot=2
        )
        self.amplitude_plot.chart_colors = ["#66c2a5", "#fc8d62"]
        self.amplitude_plot.range_unit_name = messages["finger_distance_unit"]
        self.amplitude_plot.start_range = -5
        self.amplitude_plot.end_range = 10
        self.amplitude_plot.start_domain = 0
        self.amplitude_plot.end_domain = 50

        self.add_children([self.amplitude_label, self.amplitude_plot])

class DataCollectionScreen(Container):
    def __init__(self, rect=[0, 0, 1920, 918], on_complete=None, radius=0, border_color=None):
        super().__init__([0, 0, 1920, 1080])
        
        self.task_background = TaskBackgroundContainer([0, 0, 1920, 1080],
                                                       header_title=TaskTitle(
                                                           [800, 32, 571, 42],
                                                           task_name="Finger Tapping",
                                                           task_icon=read_asset_image("finger.png")))
        
        self.data_collection_container = Container([0, 100, 1920, 918])
        
        self.data_collection_container.corner_radius = radius
        self.data_collection_container.border_color = border_color
        self.data_collection_container.alpha = 0

        self.ready_label = Label(
            [0, 40, 777, 59],
            data=messages["start_tap_ready"],
        )
        self.ready_label.font_size = 40
        self.ready_label.alignment = "center"
        self.ready_label.font_weight = "bold"

        self.keep_tapping_label = Label(
            [0, 40, 777, 57],
            data=messages["tap_finger_hint"],
        )
        self.keep_tapping_label.font_size = 40
        self.keep_tapping_label.alignment = "center"
        self.keep_tapping_label.font_weight = "bold"
        self.keep_tapping_label.visible = False

        self.stat_container = FingetTappingStatContainer([50, 134, 685, 264])

        self.analytic_container = FingetTappingAnalyticContainer(
            [50, 437, 685, 211])
        
        self.amplitude_container = FingetTappingAmplitudeContainer(
            [50, 688, 685, 201])
        
        self.amplitude_plot_container = FingetTappingAmplitudePlotContainer(
            [777, 576.97, 1094, 312]
        )

        self.main_camera_image = Image(
            [885.917, 40, 871.336, 490.08]
        )  # full length 1089.17
        self.main_camera_image.alpha = 1
        self.main_camera_image.corner_radius = 10
        self.main_camera_image.border_color = "#000000"

        self.data_collection_container.add_children([self.ready_label, 
                           self.keep_tapping_label, 
                           self.stat_container, 
                           self.analytic_container, 
                           self.amplitude_container, 
                           self.amplitude_plot_container, 
                           self.main_camera_image])
        
        self.task_background.update_content(self.data_collection_container)
        self.add_child(self.task_background)

    def start_statistic(self):
        self.keep_tapping_label.visible = True
        for i in range(len(self.children)):
            if self.data_collection_container.children[i]._id == self.keep_tapping_label._id:
                self.data_collection_container.children[i].visible = True
                break
        self.root_widget_tree.update(self.keep_tapping_label)
        self.ready_label.visible = False
        for i in range(len(self.children)):
            if self.data_collection_container.children[i]._id == self.ready_label._id:
                self.data_collection_container.children[i].visible = False
                break
        self.root_widget_tree.update(self.ready_label)
        self.root_widget_tree.update(self)
